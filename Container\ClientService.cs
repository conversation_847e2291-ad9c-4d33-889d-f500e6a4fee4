﻿using AutoMapper;
using LearnAPI.Helper;
using LearnAPI.Modal;
using LearnAPI.Repos;
using LearnAPI.Repos.Models;
using LearnAPI.Service;
using Microsoft.EntityFrameworkCore;

namespace LearnAPI.Container
{
    public class ClientService : IClientService
    {
        private readonly LearndataContext context;
        private readonly IMapper mapper;
        private readonly ILogger<ClientService> logger;

        public ClientService(LearndataContext context, IMapper mapper, ILogger<ClientService> logger)
        {
            this.context = context;
            this.mapper = mapper;
            this.logger = logger;
        }
        
        public async Task<APIResponse> Create(Clientmodal data)
        {
            APIResponse response = new APIResponse();
            try
            {
                this.logger.LogInformation("Create Begins");
                TblClient _client = this.mapper.Map<Clientmodal, TblClient>(data);
                await this.context.TblClients.AddAsync(_client);
                await this.context.SaveChangesAsync();
                response.ResponseCode = 201;
                response.Result = "pass";
            }
            catch (Exception ex)
            {
                response.ResponseCode = 400;
                response.Message = ex.Message;
                this.logger.LogError(ex.Message, ex);
            }
            return response;
        }
        

        public async Task<List<Clientmodal>> Getall()
        {
            List<Clientmodal> _response = new List<Clientmodal>();
            var _data = await this.context.TblClients.ToListAsync();
            if (_data != null)
            {
                _response = this.mapper.Map<List<TblClient>, List<Clientmodal>>(_data);
            }
            return _response;
        }

        public async Task<Clientmodal> Getbycode(string code)
        {
            Clientmodal _response = new Clientmodal();

            // Convert the string 'code' to an int before passing to FindAsync
            if (int.TryParse(code, out int parsedCode))
            {
                var _data = await this.context.TblClients.FindAsync(parsedCode);
                if (_data != null)
                {
                    _response = this.mapper.Map<TblClient, Clientmodal>(_data);
                }
            }
            return _response;
        }

        public async Task<Clientmodal> GetById(int idt)
        {
            Clientmodal _response = new Clientmodal();
            var _data = await this.context.TblClients.FindAsync(idt);
            if (_data != null)
            {
                _response = this.mapper.Map<TblClient, Clientmodal>(_data);
            }
            return _response;
        }

        public async Task<APIResponse> Remove(string code)
        {
            APIResponse response = new APIResponse();

            // Convert the string 'code' to an int before passing to FindAsync
            if (int.TryParse(code, out int parsedCode))
            {
                try
                {
                    var _client = await this.context.TblClients.FindAsync(parsedCode);
                    if (_client != null)
                    {
                        this.context.TblClients.Remove(_client);
                        await this.context.SaveChangesAsync();
                        response.ResponseCode = 200;
                        response.Result = "pass";
                    }
                    else
                    {
                        response.ResponseCode = 404;
                        response.Message = "Data not found";
                    }
                }
                catch (Exception ex)
                {
                    response.ResponseCode = 400;
                    response.Message = ex.Message;
                    this.logger.LogError(ex.Message, ex);
                }
            }
            else
            {
                response.ResponseCode = 400;
                response.Message = "Invalid code format. Code must be an integer.";
            }

            return response;
        }

        public async Task<APIResponse> Update(Clientmodal data, string code)
        {
            APIResponse response = new APIResponse();

            // Convert the string 'code' to an int before passing to FindAsync
            if (int.TryParse(code, out int parsedCode))
            {
                try
                {
                    var _client = await this.context.TblClients.FindAsync(parsedCode);
                    if (_client != null)
                    {
                        _client.rs = data.rs;
                        _client.adr1 = data.adr1;
                        _client.adr2 = data.adr2;
                        _client.tel1 = data.tel1;
                        _client.tel2 = data.tel2;
                        _client.Email = data.Email;
                        _client.mf = data.mf;
                        _client.type = data.type;
                        _client.exo = data.exo;
                        _client.fodec = data.fodec;

                        _client.IsActive = data.IsActive;
                        await this.context.SaveChangesAsync();
                        response.ResponseCode = 200;
                        response.Result = "pass";
                    }
                    else
                    {
                        response.ResponseCode = 404;
                        response.Message = "Data not found";
                    }
                }
                catch (Exception ex)
                {
                    response.ResponseCode = 400;
                    response.Message = ex.Message;
                    this.logger.LogError(ex.Message, ex);
                }
            }
            else
            {
                response.ResponseCode = 400;
                response.Message = "Invalid code format. Code must be an integer.";
            }

            return response;
        }
    }
}
