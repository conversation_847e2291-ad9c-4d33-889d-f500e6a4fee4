using System;

namespace Modal
{
    public class EmployeeVerifModel
    {
        // Employee fields
        public int? idE { get; set; }
        public string? Nom { get; set; }
        public string? Prenom { get; set; }
        public string? Matricule { get; set; }
        public string? Email { get; set; }
        public string? MotDePasse { get; set; }

        public string? Poste { get; set; }
        public string? Statut { get; set; }
        public int? RoleId { get; set; }
        public string? telephone { get; set; }
        public string? adresse { get; set; }
        public string? ville { get; set; }
        public string? projet { get; set; }
        public DateTime? date_naissance { get; set; }
        public int? age { get; set; }
        public int? type_contrat_id { get; set; }
        public int? periode_id { get; set; }
        // Verif fields
        public int? VerifId { get; set; }
        public string? Code { get; set; }
        public string? password { get; set; }
        public string? rfid { get; set; }
        public string? photoFA { get; set; }
        public string? photoEM { get; set; }
        public string? photoPoi { get; set; }
    }
}
