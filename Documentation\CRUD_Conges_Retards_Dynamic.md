# Documentation CRUD Dynamique - Congés et Retards

## Vue d'ensemble

Cette documentation décrit les nouvelles fonctionnalités CRUD (Create, Read, Update, Delete) dynamiques pour les tables `Conges` et `Retards` utilisant `ExpandoObject` pour une adaptation automatique aux changements de schéma de base de données.

## Tables concernées

### Table Conges
- **Id** (int, PK, auto-increment)
- **UtilisateurId** (int, FK)
- **DateDebut** (DateTime)
- **DateFin** (DateTime)
- **Motif** (string)
- **Statut** (string)
- **ValidePar** (int, nullable)
- **heures** (decimal, nullable)
- **solde_conge** (decimal, nullable)

### Table Retards
- **Id** (int, PK, auto-increment)
- **PointageId** (int, FK)
- **MinutesRetard** (int, nullable)
- **Justifie** (bool, nullable)
- **Commentaire** (string, nullable)

## Endpoints disponibles

### Congés

#### GET /api/EmployeeVerif/conges/dynamic-getall
Récupère tous les congés avec pagination, tri et filtrage.

**Paramètres de requête :**
- `page` (int, optionnel) : Numéro de page (défaut: 1)
- `pageSize` (int, optionnel) : Taille de page (défaut: 50, max: 1000)
- `sortBy` (string, optionnel) : Colonne de tri (défaut: "Id")
- `sortOrder` (string, optionnel) : Ordre de tri "ASC" ou "DESC" (défaut: "ASC")
- `filter` (string, optionnel) : Filtre de recherche sur Motif et Statut

**Exemple de réponse :**
```json
{
  "Data": [
    {
      "Id": 1,
      "UtilisateurId": 123,
      "DateDebut": "2024-01-15T00:00:00",
      "DateFin": "2024-01-20T00:00:00",
      "Motif": "Congés annuels",
      "Statut": "approuve",
      "ValidePar": 1,
      "heures": 40.0,
      "solde_conge": 25.5,
      "DureeJours": 6,
      "DureeSemaines": 0.86,
      "StatutCouleur": "#28a745",
      "StatutDescription": "Congé approuvé et confirmé",
      "EstFutur": false,
      "EstEnCours": false,
      "EstPasse": true
    }
  ],
  "Pagination": {
    "CurrentPage": 1,
    "PageSize": 50,
    "TotalCount": 1,
    "TotalPages": 1,
    "HasNextPage": false,
    "HasPreviousPage": false
  }
}
```

#### GET /api/EmployeeVerif/conges/dynamic-get/{id}
Récupère un congé spécifique par son ID.

#### POST /api/EmployeeVerif/conges/dynamic-create
Crée un nouveau congé.

**Corps de la requête (ExpandoObject) :**
```json
{
  "UtilisateurId": 123,
  "DateDebut": "2024-02-01",
  "DateFin": "2024-02-05",
  "Motif": "Congés maladie",
  "Statut": "en_attente"
}
```

#### PUT /api/EmployeeVerif/conges/dynamic-update/{id}
Met à jour un congé existant.

#### DELETE /api/EmployeeVerif/conges/dynamic-delete/{id}
Supprime un congé.

### Retards

#### GET /api/EmployeeVerif/retards/dynamic-getall
Récupère tous les retards avec pagination, tri et filtrage.

**Paramètres similaires aux congés**

**Exemple de réponse :**
```json
{
  "Data": [
    {
      "Id": 1,
      "PointageId": 456,
      "MinutesRetard": 15,
      "Justifie": false,
      "Commentaire": "Retard dû aux transports",
      "DureeFormatee": "0h15",
      "HeuresRetard": 0.25,
      "StatutRetard": "Non justifié",
      "StatutCouleur": "#dc3545",
      "GraviteRetard": "Modéré",
      "GraviteCouleur": "#ffc107"
    }
  ]
}
```

#### GET /api/EmployeeVerif/retards/dynamic-get/{id}
Récupère un retard spécifique par son ID.

#### POST /api/EmployeeVerif/retards/dynamic-create
Crée un nouveau retard.

**Corps de la requête :**
```json
{
  "PointageId": 456,
  "MinutesRetard": 30,
  "Justifie": true,
  "Commentaire": "Retard justifié par certificat médical"
}
```

#### PUT /api/EmployeeVerif/retards/dynamic-update/{id}
Met à jour un retard existant.

#### DELETE /api/EmployeeVerif/retards/dynamic-delete/{id}
Supprime un retard.

## Propriétés calculées

### Congés
- **DureeJours** : Nombre de jours de congé
- **DureeSemaines** : Durée en semaines (arrondie à 2 décimales)
- **StatutCouleur** : Couleur associée au statut
- **StatutDescription** : Description détaillée du statut
- **EstFutur** : Indique si le congé est dans le futur
- **EstEnCours** : Indique si le congé est en cours
- **EstPasse** : Indique si le congé est terminé

### Retards
- **DureeFormatee** : Durée formatée en heures et minutes (ex: "1h30")
- **HeuresRetard** : Durée en heures décimales
- **StatutRetard** : "Justifié" ou "Non justifié"
- **StatutCouleur** : Couleur associée au statut de justification
- **GraviteRetard** : Niveau de gravité basé sur la durée
- **GraviteCouleur** : Couleur associée à la gravité

## Gestion des erreurs

Toutes les méthodes incluent une gestion d'erreurs complète avec :
- Validation des paramètres d'entrée
- Vérification de l'existence des enregistrements
- Gestion des transactions pour les opérations de modification
- Messages d'erreur détaillés avec timestamps

## Sécurité

- Validation des noms de tables autorisées
- Utilisation de requêtes paramétrées pour éviter l'injection SQL
- Gestion des transactions avec rollback automatique en cas d'erreur
- Validation des colonnes existantes avant les opérations

## Avantages de l'approche dynamique

1. **Adaptabilité** : Ajout/suppression automatique de colonnes sans modification du code
2. **Flexibilité** : Support de tous les types de données SQL Server
3. **Maintenance** : Réduction du code de maintenance lors des évolutions de schéma
4. **Performance** : Pagination et filtrage optimisés
5. **Robustesse** : Gestion complète des erreurs et des transactions

## Tests

Les tests unitaires sont disponibles dans `Tests\CongesRetardsDynamicCrudTests.cs` et couvrent :
- Toutes les opérations CRUD
- Validation des paramètres
- Gestion des erreurs
- Pagination et filtrage
- Récupération des métadonnées de tables
