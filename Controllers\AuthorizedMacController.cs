﻿using Microsoft.AspNetCore.Mvc;
using LearnAPI.Services;
using LearnAPI.Repos;
using LearnAPI.Repos.Models;
using System.Threading.Tasks;
using System.Net.NetworkInformation;
using Microsoft.EntityFrameworkCore;

namespace LearnAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthorizedMacController : ControllerBase
    {
        private readonly LearndataContext _context;
        private readonly IEncryptionService _encryptionService;

        public AuthorizedMacController(LearndataContext context, IEncryptionService encryptionService)
        {
            _context = context;
            _encryptionService = encryptionService;
        }

        // Ajouter une adresse MAC
        //cmd => getmac
        [HttpPost("add")]
        public async Task<IActionResult> AddMac([FromBody] string macAddress)
        {
            try
            {
                var encryptedMac = _encryptionService.EncryptMacAddress(macAddress);

                var authorizedMac = new AuthorizedMac
                {
                    EncryptedMacAddress = encryptedMac
                };

                _context.AuthorizedMacs.Add(authorizedMac);
                await _context.SaveChangesAsync();

                return Ok(new { Message = $"Adresse MAC '{macAddress}' ajoutée avec succès." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }
        ////////////
        
        [HttpGet("check")]
        public async Task<IActionResult> CheckMacAddress()
        {
            try
            {
                // Récupérer toutes les adresses MAC de la machine
                var macAddresses = GetAllMacAddresses();
                if (macAddresses.Count == 0)
                {
                    return BadRequest(new { Error = "Impossible de récupérer les adresses MAC de la machine." });
                }

                // Vérifier chaque adresse MAC
                foreach (var macAddress in macAddresses)
                {
                    var encryptedMac = _encryptionService.EncryptMacAddress(macAddress);

                    // Vérifier si l'adresse MAC est autorisée
                    var authorizedMac = await _context.AuthorizedMacs
                        .FirstOrDefaultAsync(m => m.EncryptedMacAddress == encryptedMac);

                    if (authorizedMac != null)
                    {
                        return Ok(new { IsAuthorized = true, MacAddress = macAddress, Message = $"Adresse MAC '{macAddress}' est autorisée." });
                    }
                }

                // Vérifier si le code spécial existe dans la base de données
                // var specialCode = "McS-ERP Firass & Ayoub";
                var specialCode = "sBq5VYhifGtYFx1rhlKhXSm9wTOuw1HihdQWIkHgrQo =";//Crypter
                var specialCodeExists = await _context.AuthorizedMacs
                    .AnyAsync(m => m.EncryptedMacAddress == specialCode);

                if (specialCodeExists)
                {
                    return Ok(new { IsAuthorized = true, Message = "Accès autorisé via le code spécial." });
                }

                // Si aucune adresse MAC ni le code spécial ne sont autorisés
                return NotFound(new { Message = "Aucune adresse MAC autorisée et code spécial absent." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }
        private static List<string> GetAllMacAddresses()
        {
            var macAddresses = new List<string>();

            // Récupérer toutes les interfaces réseau (y compris celles déconnectées)
            var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                .Where(nic => nic.NetworkInterfaceType != NetworkInterfaceType.Loopback) // Exclure les interfaces de bouclage
                .ToList();

            foreach (var nic in networkInterfaces)
            {
                var macAddress = nic.GetPhysicalAddress().ToString();
                if (!string.IsNullOrEmpty(macAddress))
                {
                    // Formater l'adresse MAC avec des séparateurs '-'
                    var formattedMacAddress = string.Join("-", Enumerable.Range(0, 6)
                        .Select(i => macAddress.Substring(i * 2, 2))
                        .ToArray());

                    macAddresses.Add(formattedMacAddress);
                }
            }

            return macAddresses;
        }
        //[HttpGet("check")]
        //public async Task<IActionResult> CheckMacAddress()
        //{
        //    try
        //    {
        //        // Récupérer l'adresse MAC de la machine
        //        var macAddress = GetMacAddress();
        //        if (string.IsNullOrEmpty(macAddress))
        //        {
        //            return BadRequest(new { Error = "Impossible de récupérer l'adresse MAC de la machine." });
        //        }

        //        // Crypter l'adresse MAC
        //        var encryptedMac = _encryptionService.EncryptMacAddress(macAddress);

        //        // Vérifier si l'adresse MAC est autorisée
        //        var authorizedMac = await _context.AuthorizedMacs
        //            .FirstOrDefaultAsync(m => m.EncryptedMacAddress == encryptedMac);

        //        if (authorizedMac != null)
        //        {
        //            return Ok(new { IsAuthorized = true, MacAddress = macAddress, Message = $"Adresse MAC '{macAddress}' est autorisée." });
        //        }

        //        // Vérifier si le code spécial existe dans la base de données
        //        var specialCode = "McS-ERP Firass & Ayoub";
        //       // var specialCode = "sBq5VYhifGtYFx1rhlKhXSm9wTOuw1HihdQWIkHgrQo =";//Crypter
        //        var specialCodeExists = await _context.AuthorizedMacs
        //            .AnyAsync(m => m.EncryptedMacAddress == specialCode);

        //        if (specialCodeExists)
        //        {
        //            return Ok(new { IsAuthorized = true, Message = "Accès autorisé via le code spécial." });
        //        }

        //        // Si ni l'adresse MAC ni le code spécial ne sont autorisés
        //        return NotFound(new { Message = $"Adresse MAC '{macAddress}' non autorisée et code spécial absent." });
        //    }
        //    catch (Exception ex)
        //    {
        //        return BadRequest(new { Error = ex.Message });
        //    }
        //}

        //// Méthode pour récupérer l'adresse MAC de la machine
        //private static string GetMacAddress()
        //{
        //    var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
        //        .Where(nic => nic.OperationalStatus == OperationalStatus.Up && nic.NetworkInterfaceType != NetworkInterfaceType.Loopback)
        //        .FirstOrDefault();

        //    if (networkInterfaces != null)
        //    {
        //        var macAddress = networkInterfaces.GetPhysicalAddress().ToString();
        //        if (!string.IsNullOrEmpty(macAddress))
        //        {
        //            // Formater l'adresse MAC avec des séparateurs ':'
        //            return string.Join("-", Enumerable.Range(0, 6)
        //                .Select(i => macAddress.Substring(i * 2, 2))
        //                .ToArray());
        //        }
        //    }

        //    return null;
        //}
    }
}
///////////////////////////////////
///
/// 
// Vérifier si l'adresse MAC est autorisée ou si le code spécial existe
//[HttpGet("check")]
//public async Task<IActionResult> CheckMacAddress()
//{
//    try
//    {
//        // Récupérer l'adresse MAC de la machine
//        var macAddress = GetMacAddress();
//        if (string.IsNullOrEmpty(macAddress))
//        {
//            return BadRequest(new { Error = "Impossible de récupérer l'adresse MAC de la machine." });
//        }

//        // Crypter l'adresse MAC
//        var encryptedMac = _encryptionService.EncryptMacAddress(macAddress);

//        // Vérifier si l'adresse MAC est autorisée
//        var authorizedMac = await _context.AuthorizedMacs
//            .FirstOrDefaultAsync(m => m.EncryptedMacAddress == encryptedMac);

//        if (authorizedMac != null)
//        {
//            return Ok(new { IsAuthorized = true, MacAddress = macAddress, Message = $"Adresse MAC '{macAddress}' est autorisée." });
//        }

//        // Vérifier si le code spécial existe dans la base de données
//      // var specialCode = "McS-ERP Firass & Ayoub";
//       var specialCode = "sBq5VYhifGtYFx1rhlKhXSm9wTOuw1HihdQWIkHgrQo =";//Crypter
//        var specialCodeExists = await _context.AuthorizedMacs
//            .AnyAsync(m => m.EncryptedMacAddress == specialCode);

//        if (specialCodeExists)
//        {
//            return Ok(new { IsAuthorized = true, Message = "Accès autorisé via le code spécial." });
//        }

//        // Si ni l'adresse MAC ni le code spécial ne sont autorisés
//        return NotFound(new { Message = $"Adresse MAC '{macAddress}' non autorisée et code spécial absent." });
//    }
//    catch (Exception ex)
//    {
//        return BadRequest(new { Error = ex.Message });
//    }
//}

//// Méthode pour récupérer l'adresse MAC de la machine
//private static string GetMacAddress()
//{
//    var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
//        .Where(nic => nic.OperationalStatus == OperationalStatus.Up && nic.NetworkInterfaceType != NetworkInterfaceType.Loopback)
//        .FirstOrDefault();

//    if (networkInterfaces != null)
//    {
//        return networkInterfaces.GetPhysicalAddress().ToString();
//    }

//    return null;
//}
// Vérifier si l'adresse MAC est autorisée ou si le code spécial existe
/////

//using Microsoft.AspNetCore.Mvc;
//using LearnAPI.Services;
//using LearnAPI.Repos;
//using LearnAPI.Repos.Models;
//using System.Threading.Tasks;
//using System.Net.NetworkInformation;
//using Microsoft.EntityFrameworkCore;

//namespace LearnAPI.Controllers
//{
//    [ApiController]
//    [Route("api/[controller]")]
//    public class AuthorizedMacController : ControllerBase
//    {
//        private readonly LearndataContext _context;
//        private readonly IEncryptionService _encryptionService;

//        public AuthorizedMacController(LearndataContext context, IEncryptionService encryptionService)
//        {
//            _context = context;
//            _encryptionService = encryptionService;
//        }

//        // Ajouter une adresse MAC
//        [HttpPost("add")]
//        public async Task<IActionResult> AddMac([FromBody] string macAddress)
//        {
//            try
//            {
//                var encryptedMac = _encryptionService.EncryptMacAddress(macAddress);

//                var authorizedMac = new AuthorizedMac
//                {
//                    EncryptedMacAddress = encryptedMac
//                };

//                _context.AuthorizedMacs.Add(authorizedMac);
//                await _context.SaveChangesAsync();

//                return Ok(new { Message = $"Adresse MAC '{macAddress}' ajoutée avec succès." });
//            }
//            catch (Exception ex)
//            {
//                return BadRequest(new { Error = ex.Message });
//            }
//        }

//        // Vérifier si l'adresse MAC est autorisée
//        [HttpGet("check")]
//        public async Task<IActionResult> CheckMacAddress()
//        {
//            try
//            {
//                // Récupérer l'adresse MAC de la machine
//                var macAddress = GetMacAddress();
//                if (string.IsNullOrEmpty(macAddress))
//                {
//                    return BadRequest(new { Error = "Impossible de récupérer l'adresse MAC de la machine." });
//                }

//                // Crypter l'adresse MAC
//                var encryptedMac = _encryptionService.EncryptMacAddress(macAddress);

//                // Vérifier si l'adresse MAC est autorisée
//                var authorizedMac = await _context.AuthorizedMacs
//                    .FirstOrDefaultAsync(m => m.EncryptedMacAddress == encryptedMac);

//                if (authorizedMac == null)
//                {
//                    return NotFound(new { Message = $"Adresse MAC '{macAddress}' non autorisée." });
//                }

//                return Ok(new { IsAuthorized = true, MacAddress = macAddress, Message = $"Adresse MAC '{macAddress}' est autorisée." });
//            }
//            catch (Exception ex)
//            {
//                return BadRequest(new { Error = ex.Message });
//            }
//        }

//        // Méthode pour récupérer l'adresse MAC de la machine
//        private static string GetMacAddress()
//        {
//            var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
//                .Where(nic => nic.OperationalStatus == OperationalStatus.Up && nic.NetworkInterfaceType != NetworkInterfaceType.Loopback)
//                .FirstOrDefault();

//            if (networkInterfaces != null)
//            {
//                return networkInterfaces.GetPhysicalAddress().ToString();
//            }

//            return null;
//        }
//    }
//}

////Adresse physique: FC-F8-AE-50-01-6D
