﻿using System;
using System.Security.Cryptography;
using System.Text;
using System.IO;

namespace LearnAPI.Services
{
    public class EncryptionService : IEncryptionService
    {
        private readonly byte[] _key;
        private readonly byte[] _iv;

        public EncryptionService(string encryptionKey)
        {
            if (string.IsNullOrEmpty(encryptionKey))
                throw new ArgumentNullException(nameof(encryptionKey), "La clé de cryptage ne peut pas être nulle ou vide.");

            // Convertir la clé en tableau de bytes en utilisant SHA-256
            using (var sha256 = SHA256.Create())
            {
                _key = sha256.ComputeHash(Encoding.UTF8.GetBytes(encryptionKey));
            }

            // Initialisation Vector (IV) fixe pour cet exemple (16 bytes)
            _iv = new byte[16];
            Array.Copy(_key, _iv, 16); // Utiliser les 16 premiers bytes de la clé comme IV
        }

        public string Encrypt(string plainText)
        {
            if (string.IsNullOrEmpty(plainText))
                throw new ArgumentNullException(nameof(plainText), "Le texte à crypter ne peut pas être nul ou vide.");

            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = _key;
                aesAlg.IV = _iv;

                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                using (var msEncrypt = new MemoryStream())
                {
                    using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        using (var swEncrypt = new StreamWriter(csEncrypt))
                        {
                            swEncrypt.Write(plainText);
                        }
                    }
                    return Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
        }

        public string Decrypt(string cipherText)
        {
            if (string.IsNullOrEmpty(cipherText))
                throw new ArgumentNullException(nameof(cipherText), "Le texte à décrypter ne peut pas être nul ou vide.");

            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = _key;
                aesAlg.IV = _iv;

                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                using (var msDecrypt = new MemoryStream(Convert.FromBase64String(cipherText)))
                {
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        using (var srDecrypt = new StreamReader(csDecrypt))
                        {
                            return srDecrypt.ReadToEnd();
                        }
                    }
                }
            }
        }

        public string EncryptMacAddress(string macAddress)
        {
            if (string.IsNullOrEmpty(macAddress))
                throw new ArgumentNullException(nameof(macAddress), "L'adresse MAC ne peut pas être nulle ou vide.");

            // Utilise la méthode Encrypt existante
            return Encrypt(macAddress);
        }
    }
}