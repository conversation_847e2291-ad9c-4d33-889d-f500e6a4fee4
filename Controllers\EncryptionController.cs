﻿
//private static readonly string Key = "0123456789ABCDEF0123456789ABCDEF"; // 32 caractères (256 bits)
//    private static readonly string IV = ""; // 16 caractères (128 bits)
//  private readonly string key = "0123ABCDEF01234ABC56789DEF456789"; // Utilise une clé de chiffrement
//   private readonly string iv = "ABCD6789EF012345"; // Initialisation vector
using Microsoft.AspNetCore.Mvc;

public class MyController : ControllerBase
{
    private readonly IEncryptionService _encryptionService;

    public MyController(IEncryptionService encryptionService)
    {
        _encryptionService = encryptionService;
    }

    [HttpPost("encrypt")]
    public IActionResult EncryptData([FromBody] string data)
    {
        try
        {
            var encryptedData = _encryptionService.Encrypt(data);
            return Ok(new { EncryptedData = encryptedData });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message });
        }
    }

    [HttpPost("decrypt")]
    public IActionResult DecryptData([FromBody] string encryptedData)
    {
        try
        {
            var decryptedData = _encryptionService.Decrypt(encryptedData);
            return Ok(new { DecryptedData = decryptedData });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message });
        }
    }
}
