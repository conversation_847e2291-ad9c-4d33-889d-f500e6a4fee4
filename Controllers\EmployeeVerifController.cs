using Microsoft.AspNetCore.Mvc;
using System.Data.SqlClient;
using System.Data;
using System.Dynamic;

namespace Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class EmployeeVerifController : ControllerBase
    {
        [HttpPost]
        [Route("/api/Employee")]
        public async Task<IActionResult> AddEmployee([FromBody] EmployeeDto employee)
        {
            // A<PERSON>le la méthode existante pour éviter la duplication de logique
            return await CreateEmployee(employee);
        }

        // GET by Id
        [HttpGet("{id}")]
        public async Task<IActionResult> GetEmployeeById(int id)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    string query = @"SELECT * FROM [Pointage_DB].[dbo].[employee] WHERE idE = @id";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@id", id);
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                var employee = new
                                {
                                    idE = GetSafeInt(reader, "idE"),
                                    nom = GetSafeString(reader, "Nom"),
                                    prenom = GetSafeString(reader, "Prenom"),
                                    matricule = GetSafeString(reader, "Matricule"),
                                    email = GetSafeString(reader, "Email"),
                                    poste = GetSafeString(reader, "Poste"),
                                    statut = GetSafeString(reader, "Statut"),
                                    roleId = GetSafeInt(reader, "RoleId"),
                                    telephone = GetSafeString(reader, "telephone"),
                                    adresse = GetSafeString(reader, "adresse"),
                                    ville = GetSafeString(reader, "ville"),
                                    projet = GetSafeString(reader, "projet"),
                                    date_naissance = GetSafeDateTime(reader, "date_naissance"),
                                    type_contrat_id = GetSafeInt(reader, "type_contrat_id"),
                                    age = GetSafeInt(reader, "age"),
                                    periode_id = GetSafeInt(reader, "periode_id")
                                };
                                return Ok(employee);
                            }
                            else
                            {
                                return NotFound();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Erreur lors de la récupération de l'employé", message = ex.Message });
            }
        }

        // PUT (Update)
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateEmployee(int id, [FromBody] EmployeeDto employee)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    string query = @"
                        UPDATE [Pointage_DB].[dbo].[employee]
                        SET Nom = @nom, Prenom = @prenom, Matricule = @matricule, Email = @email, Poste = @poste, Statut = @statut,
                            RoleId = @roleId, telephone = @telephone, adresse = @adresse, ville = @ville, projet = @projet,
                            date_naissance = @date_naissance, type_contrat_id = @type_contrat_id, periode_id = @periode_id,
                            username = @username, departement_id = @departement_id, password = @password, rfid = @rfid,
                            photoFA = @photoFA, photoEM = @photoEM, photoPoi = @photoPoi
                        WHERE idE = @id";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@id", id);
                        command.Parameters.AddWithValue("@nom", employee.Nom ?? "");
                        command.Parameters.AddWithValue("@prenom", employee.Prenom ?? "");
                        command.Parameters.AddWithValue("@matricule", employee.Matricule ?? "");
                        command.Parameters.AddWithValue("@email", employee.Email ?? "");
                        command.Parameters.AddWithValue("@poste", employee.Poste ?? "");
                        object statutValue;
                        if (employee.Statut == null)
                        {
                            statutValue = 1;
                        }
                        else if (employee.Statut.ToLower() == "actif" || employee.Statut == "1" || employee.Statut.ToLower() == "true")
                        {
                            statutValue = 1;
                        }
                        else
                        {
                            statutValue = 0;
                        }
                        command.Parameters.AddWithValue("@statut", statutValue);
                        command.Parameters.AddWithValue("@roleId", employee.RoleId.HasValue ? (object)employee.RoleId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@telephone", employee.Telephone ?? "");
                        command.Parameters.AddWithValue("@adresse", employee.Adresse ?? "");
                        command.Parameters.AddWithValue("@ville", employee.Ville ?? "");
                        command.Parameters.AddWithValue("@projet", employee.Projet ?? "");
                        command.Parameters.AddWithValue("@date_naissance", employee.DateNaissance.HasValue ? (object)employee.DateNaissance.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@type_contrat_id", employee.TypeContratId.HasValue ? (object)employee.TypeContratId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@periode_id", employee.PeriodeId.HasValue ? (object)employee.PeriodeId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@username", employee.Username ?? "");
                        command.Parameters.AddWithValue("@departement_id", employee.DepartementId.HasValue ? (object)employee.DepartementId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@password", employee.Password ?? "");
                        command.Parameters.AddWithValue("@rfid", employee.Rfid ?? "");
                        command.Parameters.AddWithValue("@photoFA", employee.PhotoFA ?? "");
                        command.Parameters.AddWithValue("@photoEM", employee.PhotoEM ?? "");
                        command.Parameters.AddWithValue("@photoPoi", employee.PhotoPoi ?? "");

                        await connection.OpenAsync();
                        int rows = await command.ExecuteNonQueryAsync();
                        if (rows > 0)
                        {
                            return Ok(new { message = "Employé mis à jour avec succès." });
                        }
                        else
                        {
                            return NotFound();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Erreur lors de la mise à jour de l'employé", message = ex.Message });
            }
        }

        // DELETE
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmployee(int id)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    string query = "DELETE FROM [Pointage_DB].[dbo].[employee] WHERE idE = @id";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@id", id);
                        await connection.OpenAsync();
                        int rows = await command.ExecuteNonQueryAsync();
                        if (rows > 0)
                        {
                            return Ok(new { message = "Employé supprimé avec succès." });
                        }
                        else
                        {
                            return NotFound();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Erreur lors de la suppression de l'employé", message = ex.Message });
            }
        }
        private readonly string _connectionString = "Server=DESKTOP-JM9HQ35\\MSSQLSERVER01;Database=Pointage_DB;Trusted_Connection=True;";

        public EmployeeVerifController()
        {
        }

        [HttpPost("create")]
        public async Task<IActionResult> CreateEmployee([FromBody] EmployeeDto employee)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    string query = @"
                        INSERT INTO [Pointage_DB].[dbo].[employee]
                        (Nom, Prenom, Matricule, Email, Poste, Statut, RoleId, telephone,
                         adresse, ville, projet, date_naissance, type_contrat_id, periode_id, username, departement_id)
                        VALUES
                        (@nom, @prenom, @matricule, @email, @poste, @statut, @roleId, @telephone,
                         @adresse, @ville, @projet, @date_naissance, @type_contrat_id, @periode_id, @username, @departement_id);
                        SELECT SCOPE_IDENTITY();";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@nom", employee.Nom ?? "");
                        command.Parameters.AddWithValue("@prenom", employee.Prenom ?? "");
                        command.Parameters.AddWithValue("@matricule", employee.Matricule ?? "");
                        command.Parameters.AddWithValue("@email", employee.Email ?? "");
                        command.Parameters.AddWithValue("@poste", employee.Poste ?? "");
                        // Si la colonne Statut est de type bit, on convertit la valeur string en booléen (1 = actif, 0 = inactif)
                        object statutValue;
                        if (employee.Statut == null)
                        {
                            statutValue = 1; // Par défaut actif
                        }
                        else if (employee.Statut.ToLower() == "actif" || employee.Statut == "1" || employee.Statut.ToLower() == "true")
                        {
                            statutValue = 1;
                        }
                        else
                        {
                            statutValue = 0;
                        }
                        command.Parameters.AddWithValue("@statut", statutValue);
                        command.Parameters.AddWithValue("@roleId", employee.RoleId.HasValue ? (object)employee.RoleId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@telephone", employee.Telephone ?? "");
                        command.Parameters.AddWithValue("@adresse", employee.Adresse ?? "");
                        command.Parameters.AddWithValue("@ville", employee.Ville ?? "");
                        command.Parameters.AddWithValue("@projet", employee.Projet ?? "");
                        command.Parameters.AddWithValue("@date_naissance", employee.DateNaissance.HasValue ? (object)employee.DateNaissance.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@type_contrat_id", employee.TypeContratId.HasValue ? (object)employee.TypeContratId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@periode_id", employee.PeriodeId.HasValue ? (object)employee.PeriodeId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@username", employee.Username ?? "");
                        command.Parameters.AddWithValue("@departement_id", employee.DepartementId.HasValue ? (object)employee.DepartementId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@password", employee.Password ?? "");
                        command.Parameters.AddWithValue("@rfid", employee.Rfid ?? "");
                        command.Parameters.AddWithValue("@photoFA", employee.PhotoFA ?? "");
                        command.Parameters.AddWithValue("@photoEM", employee.PhotoEM ?? "");
                        command.Parameters.AddWithValue("@photoPoi", employee.PhotoPoi ?? "");

                        await connection.OpenAsync();
                        var newId = await command.ExecuteScalarAsync();
                        
                        var result = new
                        {
                            idE = Convert.ToInt32(newId),
                            nom = employee.Nom,
                            prenom = employee.Prenom,
                            matricule = employee.Matricule,
                            email = employee.Email,
                            poste = employee.Poste,
                            statut = employee.Statut,
                            roleId = employee.RoleId,
                            telephone = employee.Telephone,
                            adresse = employee.Adresse,
                            ville = employee.Ville,
                            projet = employee.Projet,
                            dateNaissance = employee.DateNaissance,
                            typeContratId = employee.TypeContratId,
                            periodeId = employee.PeriodeId,
                            username = employee.Username,
                            departementId = employee.DepartementId,
                            code = employee.Code,
                            password = employee.Password,
                            rfid = employee.Rfid,
                            photoFA = employee.PhotoFA,
                            photoEM = employee.PhotoEM,
                            photoPoi = employee.PhotoPoi
                        };

                        return Ok(result);
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    error = "Erreur lors de la création de l'employé", 
                    message = ex.Message,
                    details = ex.InnerException?.Message 
                });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetAllEmployees()
        {
            try
            {
                List<object> employees = new List<object>();

                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    string query = @"SELECT [idE], [Nom], [Prenom], [Matricule], [Email], [Poste], [Statut],
                                    [RoleId], [telephone], [adresse], [ville], [projet], [date_naissance],
                                    [type_contrat_id], [age], [periode_id]
                                    FROM [Pointage_DB].[dbo].[employee] ORDER BY idE DESC";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                employees.Add(new
                                {
                                    idE = reader.GetInt32("idE"),
                                    nom = GetSafeString(reader, "Nom"),
                                    prenom = GetSafeString(reader, "Prenom"),
                                    matricule = GetSafeString(reader, "Matricule"),
                                    email = GetSafeString(reader, "Email"),
                                    poste = GetSafeString(reader, "Poste"),
                                    statut = GetSafeString(reader, "Statut"),
                                    roleId = GetSafeInt(reader, "RoleId"),
                                    telephone = GetSafeString(reader, "telephone"),
                                    adresse = GetSafeString(reader, "adresse"),
                                    ville = GetSafeString(reader, "ville"),
                                    projet = GetSafeString(reader, "projet"),
                                    date_naissance = GetSafeDateTime(reader, "date_naissance"),
                                    type_contrat_id = GetSafeInt(reader, "type_contrat_id"),
                                    age = GetSafeInt(reader, "age"),
                                    periode_id = GetSafeInt(reader, "periode_id")
                                });
                            }
                        }
                    }
                }

                return Ok(employees);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    error = "Erreur lors de la récupération des employés",
                    message = ex.Message
                });
            }
        }

        [HttpGet("getAll")]
        public async Task<IActionResult> GetAllEmployeesAlternate()
        {
            try
            {
                List<object> employees = new List<object>();

                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    string query = @"SELECT [idE], [Nom], [Prenom], [Matricule], [Email], [Poste], [Statut],
                                    [RoleId], [telephone], [adresse], [ville], [projet], [date_naissance],
                                    [type_contrat_id], [age], [periode_id]
                                    FROM [Pointage_DB].[dbo].[employee] ORDER BY idE DESC";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                employees.Add(new
                                {
                                    idE = reader.GetInt32("idE"),
                                    nom = GetSafeString(reader, "Nom"),
                                    prenom = GetSafeString(reader, "Prenom"),
                                    matricule = GetSafeString(reader, "Matricule"),
                                    email = GetSafeString(reader, "Email"),
                                    poste = GetSafeString(reader, "Poste"),
                                    statut = GetSafeString(reader, "Statut"),
                                    roleId = GetSafeInt(reader, "RoleId"),
                                    telephone = GetSafeString(reader, "telephone"),
                                    adresse = GetSafeString(reader, "adresse"),
                                    ville = GetSafeString(reader, "ville"),
                                    projet = GetSafeString(reader, "projet"),
                                    date_naissance = GetSafeDateTime(reader, "date_naissance"),
                                    type_contrat_id = GetSafeInt(reader, "type_contrat_id"),
                                    age = GetSafeInt(reader, "age"),
                                    periode_id = GetSafeInt(reader, "periode_id")
                                });
                            }
                        }
                    }
                }

                return Ok(employees);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    error = "Erreur lors de la récupération des employés",
                    message = ex.Message
                });
            }
        }

        [HttpGet("tableau-pointage")]
        public async Task<IActionResult> GetTableauPointage([FromQuery] DateTime? date = null)
        {
            try
            {
                // Si aucune date n'est fournie, utiliser la date d'aujourd'hui
                DateTime datePointage = date ?? DateTime.Today;

                var tableauPointage = new List<ExpandoObject>();

                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    string query = @"
                        SELECT
                            e.[idE], e.[Nom], e.[Prenom], e.[Matricule], e.[Email], e.[Poste], e.[Statut],
                            p.[heure_arrivee], p.[heure_depart], p.[date_pointage], p.[statut] as statut_pointage
                        FROM [Pointage_DB].[dbo].[employee] e
                        LEFT JOIN [Pointage_DB].[dbo].[pointage] p ON e.[idE] = p.[employee_id]
                            AND CAST(p.[date_pointage] AS DATE) = @datePointage
                        WHERE e.[Statut] = 'Actif'
                        ORDER BY e.[Nom], e.[Prenom]";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@datePointage", datePointage.Date);
                        await connection.OpenAsync();

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                dynamic pointage = new ExpandoObject();

                                // Informations de base de l'employé
                                pointage.IdEmploye = reader.GetInt32("idE");
                                pointage.Nom = reader.IsDBNull("Nom") ? "" : reader.GetString("Nom");
                                pointage.Prenom = reader.IsDBNull("Prenom") ? "" : reader.GetString("Prenom");
                                pointage.NomComplet = $"{pointage.Prenom} {pointage.Nom}";
                                pointage.Matricule = reader.IsDBNull("Matricule") ? "" : reader.GetString("Matricule");
                                pointage.Email = reader.IsDBNull("Email") ? "" : reader.GetString("Email");
                                pointage.Poste = reader.IsDBNull("Poste") ? "" : reader.GetString("Poste");
                                pointage.DatePointage = datePointage.ToString("yyyy-MM-dd");

                                // Heures de pointage
                                DateTime? heureArrivee = reader.IsDBNull("heure_arrivee") ? null : reader.GetDateTime("heure_arrivee");
                                DateTime? heureDepart = reader.IsDBNull("heure_depart") ? null : reader.GetDateTime("heure_depart");

                                pointage.HeureArrivee = heureArrivee?.ToString("HH:mm") ?? "";
                                pointage.HeureDepart = heureDepart?.ToString("HH:mm") ?? "";

                                // Calcul de la durée travaillée
                                if (heureArrivee.HasValue && heureDepart.HasValue)
                                {
                                    TimeSpan duree = heureDepart.Value - heureArrivee.Value;
                                    pointage.DureeTravaillee = $"{(int)duree.TotalHours:D2}h{duree.Minutes:D2}";
                                    pointage.DureeTravailleeMinutes = (int)duree.TotalMinutes;
                                }
                                else if (heureArrivee.HasValue && !heureDepart.HasValue)
                                {
                                    // Calculer la durée depuis l'arrivée jusqu'à maintenant
                                    TimeSpan dureeActuelle = DateTime.Now - heureArrivee.Value;
                                    pointage.DureeTravaillee = $"{(int)dureeActuelle.TotalHours:D2}h{dureeActuelle.Minutes:D2} (en cours)";
                                    pointage.DureeTravailleeMinutes = (int)dureeActuelle.TotalMinutes;
                                }
                                else
                                {
                                    pointage.DureeTravaillee = "";
                                    pointage.DureeTravailleeMinutes = 0;
                                }

                                // Détermination du statut
                                string statut = DeterminerStatut(heureArrivee, heureDepart, datePointage);
                                pointage.Statut = statut;
                                pointage.StatutCouleur = GetStatutCouleur(statut);

                                // Informations supplémentaires
                                pointage.EstEnRetard = EstEnRetard(heureArrivee);
                                pointage.HeureArriveeComplete = heureArrivee?.ToString("yyyy-MM-dd HH:mm:ss") ?? "";
                                pointage.HeureDepartComplete = heureDepart?.ToString("yyyy-MM-dd HH:mm:ss") ?? "";

                                tableauPointage.Add(pointage);
                            }
                        }
                    }
                }

                // Statistiques globales
                var statistiques = new ExpandoObject() as IDictionary<string, object>;
                statistiques["DatePointage"] = datePointage.ToString("yyyy-MM-dd");
                statistiques["TotalEmployes"] = tableauPointage.Count;
                statistiques["Presents"] = tableauPointage.Count(p => ((dynamic)p).Statut == "Présent");
                statistiques["Absents"] = tableauPointage.Count(p => ((dynamic)p).Statut == "Absent");
                statistiques["EnRetard"] = tableauPointage.Count(p => ((dynamic)p).Statut == "En retard");
                statistiques["NonEncorePartis"] = tableauPointage.Count(p => ((dynamic)p).Statut == "Non encore parti");

                var resultat = new
                {
                    Statistiques = statistiques,
                    Pointages = tableauPointage
                };

                return Ok(resultat);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    error = "Erreur lors de la récupération du tableau de pointage",
                    message = ex.Message
                });
            }
        }

        private string DeterminerStatut(DateTime? heureArrivee, DateTime? heureDepart, DateTime datePointage)
        {
            // Si c'est une date future, on ne peut pas déterminer le statut
            if (datePointage.Date > DateTime.Today)
                return "À venir";

            // Si pas d'heure d'arrivée, l'employé est absent
            if (!heureArrivee.HasValue)
                return "Absent";

            // Si heure d'arrivée mais pas de départ
            if (heureArrivee.HasValue && !heureDepart.HasValue)
            {
                // Si c'est aujourd'hui, il est encore au travail
                if (datePointage.Date == DateTime.Today)
                    return "Non encore parti";
                // Si c'est un jour passé sans heure de départ, c'est anormal
                else
                    return "Départ non enregistré";
            }

            // Si les deux heures sont présentes
            if (heureArrivee.HasValue && heureDepart.HasValue)
            {
                // Vérifier si en retard (après 8h30 par exemple)
                TimeSpan heureArriveeTime = heureArrivee.Value.TimeOfDay;
                TimeSpan heureLimite = new TimeSpan(8, 30, 0); // 8h30

                if (heureArriveeTime > heureLimite)
                    return "En retard";
                else
                    return "Présent";
            }

            return "Indéterminé";
        }

        private bool EstEnRetard(DateTime? heureArrivee)
        {
            if (!heureArrivee.HasValue) return false;

            TimeSpan heureArriveeTime = heureArrivee.Value.TimeOfDay;
            TimeSpan heureLimite = new TimeSpan(8, 30, 0); // 8h30

            return heureArriveeTime > heureLimite;
        }

        private string GetStatutCouleur(string statut)
        {
            return statut switch
            {
                "Présent" => "#28a745", // Vert
                "Absent" => "#dc3545", // Rouge
                "En retard" => "#ffc107", // Jaune/Orange
                "Non encore parti" => "#17a2b8", // Bleu
                "Départ non enregistré" => "#fd7e14", // Orange
                "À venir" => "#6c757d", // Gris
                _ => "#6c757d" // Gris par défaut
            };
        }

        // Méthodes helper pour la lecture sécurisée des données
        private string GetSafeString(SqlDataReader reader, string columnName)
        {
            try
            {
                if (reader.IsDBNull(columnName))
                    return "";

                var value = reader[columnName];
                if (value is bool boolValue)
                    return boolValue.ToString();

                return value?.ToString() ?? "";
            }
            catch
            {
                return "";
            }
        }

        private int? GetSafeInt(SqlDataReader reader, string columnName)
        {
            try
            {
                if (reader.IsDBNull(columnName))
                    return null;

                var value = reader[columnName];
                if (value is int intValue)
                    return intValue;
                if (value is bool boolValue)
                    return boolValue ? 1 : 0;
                if (int.TryParse(value?.ToString(), out int parsedValue))
                    return parsedValue;

                return null;
            }
            catch
            {
                return null;
            }
        }

        private DateTime? GetSafeDateTime(SqlDataReader reader, string columnName)
        {
            try
            {
                if (reader.IsDBNull(columnName))
                    return null;

                var value = reader[columnName];
                if (value is DateTime dateValue)
                    return dateValue;
                if (DateTime.TryParse(value?.ToString(), out DateTime parsedValue))
                    return parsedValue;

                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Méthode dynamic-join qui combine dynamiquement les tables Employee et Verif
        /// en utilisant ExpandoObject pour gérer automatiquement l'ajout/suppression de colonnes
        /// </summary>
        [HttpGet("dynamic-join")]
        public async Task<IActionResult> GetEmployeeVerifDynamicJoin([FromQuery] int? employeeId = null)
        {
            try
            {
                var results = new List<ExpandoObject>();

                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    // Requête dynamique qui récupère toutes les colonnes des deux tables
                    string query = @"
                        SELECT
                            e.*,
                            v.Id as VerifId,
                            v.Code as VerifCode,
                            v.password as VerifPassword,
                            v.rfid as VerifRfid,
                            v.photoFA as VerifPhotoFA,
                            v.photoEM as VerifPhotoEM,
                            v.photoPoi as VerifPhotoPoi
                        FROM [Pointage_DB].[dbo].[employee] e
                        LEFT JOIN [Pointage_DB].[dbo].[verif] v ON e.[idE] = v.[employee_id]";

                    // Ajouter une condition WHERE si un employeeId est spécifié
                    if (employeeId.HasValue)
                    {
                        query += " WHERE e.[idE] = @employeeId";
                    }

                    query += " ORDER BY e.[idE]";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        if (employeeId.HasValue)
                        {
                            command.Parameters.AddWithValue("@employeeId", employeeId.Value);
                        }

                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                // Créer un ExpandoObject dynamique
                                dynamic employeeVerif = new ExpandoObject();
                                var employeeVerifDict = (IDictionary<string, object>)employeeVerif;

                                // Parcourir dynamiquement toutes les colonnes retournées
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    string columnName = reader.GetName(i);
                                    object? value = reader.IsDBNull(i) ? null : reader.GetValue(i);

                                    // Traitement spécial pour certains types de données
                                    if (value != null)
                                    {
                                        // Conversion des booléens en string pour une meilleure lisibilité
                                        if (value is bool boolValue)
                                        {
                                            value = boolValue ? "Actif" : "Inactif";
                                        }
                                        // Formatage des dates
                                        else if (value is DateTime dateValue)
                                        {
                                            value = dateValue.ToString("yyyy-MM-dd HH:mm:ss");
                                        }
                                    }

                                    // Ajouter la propriété à l'objet dynamique
                                    employeeVerifDict[columnName] = value ?? "";
                                }

                                // Ajouter des propriétés calculées dynamiques
                                AddCalculatedProperties(employeeVerifDict);

                                results.Add(employeeVerif);
                            }
                        }
                    }
                }

                // Métadonnées sur la structure dynamique
                var metadata = await GetTableMetadata();

                var response = new
                {
                    Data = results,
                    Metadata = metadata,
                    Count = results.Count,
                    Timestamp = DateTime.UtcNow
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    error = "Erreur lors de la récupération des données Employee-Verif",
                    message = ex.Message,
                    details = ex.InnerException?.Message
                });
            }
        }

        /// <summary>
        /// Ajoute des propriétés calculées dynamiques à l'objet
        /// </summary>
        private void AddCalculatedProperties(IDictionary<string, object> employeeVerifDict)
        {
            // Nom complet
            string nom = employeeVerifDict.ContainsKey("Nom") ? employeeVerifDict["Nom"]?.ToString() ?? "" : "";
            string prenom = employeeVerifDict.ContainsKey("Prenom") ? employeeVerifDict["Prenom"]?.ToString() ?? "" : "";
            employeeVerifDict["NomComplet"] = $"{prenom} {nom}".Trim();

            // Statut de vérification
            bool hasVerifData = employeeVerifDict.ContainsKey("VerifId") && employeeVerifDict["VerifId"] != null;
            employeeVerifDict["HasVerificationData"] = hasVerifData;
            employeeVerifDict["VerificationStatus"] = hasVerifData ? "Configuré" : "Non configuré";

            // Calcul de l'âge si date de naissance disponible
            if (employeeVerifDict.ContainsKey("date_naissance") && employeeVerifDict["date_naissance"] != null)
            {
                if (DateTime.TryParse(employeeVerifDict["date_naissance"].ToString(), out DateTime dateNaissance))
                {
                    int age = DateTime.Today.Year - dateNaissance.Year;
                    if (dateNaissance.Date > DateTime.Today.AddYears(-age)) age--;
                    employeeVerifDict["AgeCalcule"] = age;
                }
            }

            // Indicateurs de sécurité
            bool hasPassword = employeeVerifDict.ContainsKey("VerifPassword") && !string.IsNullOrEmpty(employeeVerifDict["VerifPassword"]?.ToString());
            bool hasRfid = employeeVerifDict.ContainsKey("VerifRfid") && !string.IsNullOrEmpty(employeeVerifDict["VerifRfid"]?.ToString());
            bool hasPhoto = employeeVerifDict.ContainsKey("VerifPhotoFA") && !string.IsNullOrEmpty(employeeVerifDict["VerifPhotoFA"]?.ToString());

            employeeVerifDict["SecurityLevel"] = GetSecurityLevel(hasPassword, hasRfid, hasPhoto);
            employeeVerifDict["AuthenticationMethods"] = GetAuthenticationMethods(hasPassword, hasRfid, hasPhoto);
        }

        /// <summary>
        /// Détermine le niveau de sécurité basé sur les méthodes d'authentification disponibles
        /// </summary>
        private string GetSecurityLevel(bool hasPassword, bool hasRfid, bool hasPhoto)
        {
            int methodCount = (hasPassword ? 1 : 0) + (hasRfid ? 1 : 0) + (hasPhoto ? 1 : 0);

            return methodCount switch
            {
                0 => "Aucune",
                1 => "Basique",
                2 => "Moyenne",
                3 => "Élevée",
                _ => "Indéterminée"
            };
        }

        /// <summary>
        /// Retourne la liste des méthodes d'authentification disponibles
        /// </summary>
        private List<string> GetAuthenticationMethods(bool hasPassword, bool hasRfid, bool hasPhoto)
        {
            var methods = new List<string>();

            if (hasPassword) methods.Add("Mot de passe");
            if (hasRfid) methods.Add("RFID");
            if (hasPhoto) methods.Add("Reconnaissance faciale");

            return methods;
        }

        /// <summary>
        /// Récupère les métadonnées des tables pour documenter la structure dynamique
        /// </summary>
        private async Task<object> GetTableMetadata()
        {
            try
            {
                var employeeColumns = new List<object>();
                var verifColumns = new List<object>();

                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // Métadonnées de la table Employee
                    string employeeMetaQuery = @"
                        SELECT
                            COLUMN_NAME,
                            DATA_TYPE,
                            IS_NULLABLE,
                            CHARACTER_MAXIMUM_LENGTH,
                            COLUMN_DEFAULT
                        FROM INFORMATION_SCHEMA.COLUMNS
                        WHERE TABLE_NAME = 'employee' AND TABLE_SCHEMA = 'dbo'
                        ORDER BY ORDINAL_POSITION";

                    using (SqlCommand command = new SqlCommand(employeeMetaQuery, connection))
                    {
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                employeeColumns.Add(new
                                {
                                    ColumnName = reader["COLUMN_NAME"].ToString(),
                                    DataType = reader["DATA_TYPE"].ToString(),
                                    IsNullable = reader["IS_NULLABLE"].ToString(),
                                    MaxLength = reader["CHARACTER_MAXIMUM_LENGTH"]?.ToString(),
                                    DefaultValue = reader["COLUMN_DEFAULT"]?.ToString()
                                });
                            }
                        }
                    }

                    // Métadonnées de la table Verif
                    string verifMetaQuery = @"
                        SELECT
                            COLUMN_NAME,
                            DATA_TYPE,
                            IS_NULLABLE,
                            CHARACTER_MAXIMUM_LENGTH,
                            COLUMN_DEFAULT
                        FROM INFORMATION_SCHEMA.COLUMNS
                        WHERE TABLE_NAME = 'verif' AND TABLE_SCHEMA = 'dbo'
                        ORDER BY ORDINAL_POSITION";

                    using (SqlCommand command = new SqlCommand(verifMetaQuery, connection))
                    {
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                verifColumns.Add(new
                                {
                                    ColumnName = reader["COLUMN_NAME"].ToString(),
                                    DataType = reader["DATA_TYPE"].ToString(),
                                    IsNullable = reader["IS_NULLABLE"].ToString(),
                                    MaxLength = reader["CHARACTER_MAXIMUM_LENGTH"]?.ToString(),
                                    DefaultValue = reader["COLUMN_DEFAULT"]?.ToString()
                                });
                            }
                        }
                    }
                }

                return new
                {
                    EmployeeTable = new
                    {
                        TableName = "employee",
                        Columns = employeeColumns
                    },
                    VerifTable = new
                    {
                        TableName = "verif",
                        Columns = verifColumns
                    },
                    CalculatedProperties = new[]
                    {
                        "NomComplet",
                        "HasVerificationData",
                        "VerificationStatus",
                        "AgeCalcule",
                        "SecurityLevel",
                        "AuthenticationMethods"
                    }
                };
            }
            catch (Exception ex)
            {
                return new { Error = $"Erreur lors de la récupération des métadonnées: {ex.Message}" };
            }
        }

        /// <summary>
        /// Récupère la structure des colonnes pour une table spécifique de manière dynamique
        /// </summary>
        [HttpGet("dynamic-columns/{tableName}")]
        public async Task<IActionResult> GetDynamicTableColumns(string tableName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(tableName))
                {
                    return BadRequest(new { message = "Le nom de la table est requis." });
                }

                // Validation des noms de tables autorisées pour la sécurité
                var allowedTables = new[] { "employee", "verif", "pointage", "Conges", "Retards" };
                if (!allowedTables.Contains(tableName.ToLower()))
                {
                    return BadRequest(new { message = "Table non autorisée." });
                }

                var columns = new List<object>();

                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    string query = @"
                        SELECT
                            COLUMN_NAME,
                            DATA_TYPE,
                            IS_NULLABLE,
                            CHARACTER_MAXIMUM_LENGTH,
                            NUMERIC_PRECISION,
                            NUMERIC_SCALE,
                            COLUMN_DEFAULT,
                            ORDINAL_POSITION
                        FROM INFORMATION_SCHEMA.COLUMNS
                        WHERE TABLE_NAME = @tableName AND TABLE_SCHEMA = 'dbo'
                        ORDER BY ORDINAL_POSITION";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@tableName", tableName);

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                columns.Add(new
                                {
                                    ColumnName = reader["COLUMN_NAME"].ToString(),
                                    DataType = reader["DATA_TYPE"].ToString(),
                                    IsNullable = reader["IS_NULLABLE"].ToString() == "YES",
                                    MaxLength = reader["CHARACTER_MAXIMUM_LENGTH"]?.ToString(),
                                    NumericPrecision = reader["NUMERIC_PRECISION"]?.ToString(),
                                    NumericScale = reader["NUMERIC_SCALE"]?.ToString(),
                                    DefaultValue = reader["COLUMN_DEFAULT"]?.ToString(),
                                    Position = Convert.ToInt32(reader["ORDINAL_POSITION"])
                                });
                            }
                        }
                    }
                }

                var response = new
                {
                    TableName = tableName,
                    Columns = columns,
                    ColumnCount = columns.Count,
                    Timestamp = DateTime.UtcNow
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    error = "Erreur lors de la récupération de la structure de la table",
                    message = ex.Message,
                    tableName = tableName
                });
            }
        }

        /// <summary>
        /// Met à jour dynamiquement un enregistrement Employee-Verif en utilisant ExpandoObject
        /// </summary>
        [HttpPut("dynamic-update/{employeeId}")]
        public async Task<IActionResult> UpdateEmployeeVerifDynamic(int employeeId, [FromBody] ExpandoObject updateData)
        {
            try
            {
                var updateDict = updateData as IDictionary<string, object>;
                if (updateDict == null)
                {
                    return BadRequest(new { message = "Format de données invalide." });
                }

                if (!updateDict.Any())
                {
                    return BadRequest(new { message = "Aucune donnée à mettre à jour." });
                }

                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Séparer les champs Employee et Verif
                            var employeeFields = new Dictionary<string, object>();
                            var verifFields = new Dictionary<string, object>();

                            // Récupérer la structure des tables pour savoir quels champs appartiennent à quelle table
                            var employeeColumns = await GetTableColumnsFromDb(connection, transaction, "employee");
                            var verifColumns = await GetTableColumnsFromDb(connection, transaction, "verif");

                            foreach (var kvp in updateDict)
                            {
                                string fieldName = kvp.Key;

                                // Ignorer les champs calculés
                                if (IsCalculatedField(fieldName)) continue;

                                // Déterminer à quelle table appartient le champ
                                if (employeeColumns.Contains(fieldName, StringComparer.OrdinalIgnoreCase))
                                {
                                    employeeFields[fieldName] = kvp.Value;
                                }
                                else if (verifColumns.Contains(fieldName, StringComparer.OrdinalIgnoreCase) ||
                                        fieldName.StartsWith("Verif", StringComparison.OrdinalIgnoreCase))
                                {
                                    // Nettoyer le nom du champ pour la table verif
                                    string cleanFieldName = fieldName.StartsWith("Verif", StringComparison.OrdinalIgnoreCase)
                                        ? fieldName.Substring(5)
                                        : fieldName;
                                    verifFields[cleanFieldName] = kvp.Value;
                                }
                            }

                            // Mettre à jour la table Employee si nécessaire
                            if (employeeFields.Any())
                            {
                                await UpdateEmployeeDynamic(connection, transaction, employeeId, employeeFields);
                            }

                            // Mettre à jour la table Verif si nécessaire
                            if (verifFields.Any())
                            {
                                await UpdateVerifDynamic(connection, transaction, employeeId, verifFields);
                            }

                            transaction.Commit();

                            return Ok(new
                            {
                                message = "Mise à jour réussie",
                                employeeId = employeeId,
                                updatedEmployeeFields = employeeFields.Keys,
                                updatedVerifFields = verifFields.Keys,
                                timestamp = DateTime.UtcNow
                            });
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    error = "Erreur lors de la mise à jour dynamique",
                    message = ex.Message,
                    employeeId = employeeId
                });
            }
        }

        /// <summary>
        /// Vérifie si un champ est un champ calculé
        /// </summary>
        private bool IsCalculatedField(string fieldName)
        {
            var calculatedFields = new[]
            {
                "NomComplet", "HasVerificationData", "VerificationStatus",
                "AgeCalcule", "SecurityLevel", "AuthenticationMethods"
            };

            return calculatedFields.Contains(fieldName, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Récupère la liste des colonnes d'une table
        /// </summary>
        private async Task<List<string>> GetTableColumnsFromDb(SqlConnection connection, SqlTransaction transaction, string tableName)
        {
            var columns = new List<string>();

            string query = @"
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = @tableName AND TABLE_SCHEMA = 'dbo'";

            using (SqlCommand command = new SqlCommand(query, connection, transaction))
            {
                command.Parameters.AddWithValue("@tableName", tableName);

                using (SqlDataReader reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        columns.Add(reader["COLUMN_NAME"].ToString() ?? "");
                    }
                }
            }

            return columns;
        }

        /// <summary>
        /// Met à jour dynamiquement la table Employee
        /// </summary>
        private async Task UpdateEmployeeDynamic(SqlConnection connection, SqlTransaction transaction, int employeeId, Dictionary<string, object> fields)
        {
            if (!fields.Any()) return;

            var setClauses = new List<string>();
            var parameters = new List<SqlParameter>();

            foreach (var kvp in fields)
            {
                setClauses.Add($"[{kvp.Key}] = @{kvp.Key}");
                parameters.Add(new SqlParameter($"@{kvp.Key}", kvp.Value ?? DBNull.Value));
            }

            string query = $@"
                UPDATE [Pointage_DB].[dbo].[employee]
                SET {string.Join(", ", setClauses)}
                WHERE [idE] = @employeeId";

            using (SqlCommand command = new SqlCommand(query, connection, transaction))
            {
                command.Parameters.AddRange(parameters.ToArray());
                command.Parameters.AddWithValue("@employeeId", employeeId);

                await command.ExecuteNonQueryAsync();
            }
        }

        /// <summary>
        /// Met à jour dynamiquement la table Verif
        /// </summary>
        private async Task UpdateVerifDynamic(SqlConnection connection, SqlTransaction transaction, int employeeId, Dictionary<string, object> fields)
        {
            if (!fields.Any()) return;

            // Vérifier si un enregistrement Verif existe pour cet employé
            string checkQuery = "SELECT COUNT(*) FROM [Pointage_DB].[dbo].[verif] WHERE [employee_id] = @employeeId";

            using (SqlCommand checkCommand = new SqlCommand(checkQuery, connection, transaction))
            {
                checkCommand.Parameters.AddWithValue("@employeeId", employeeId);
                var result = await checkCommand.ExecuteScalarAsync();
                int count = result != null ? Convert.ToInt32(result) : 0;

                if (count > 0)
                {
                    // Mise à jour
                    var setClauses = new List<string>();
                    var parameters = new List<SqlParameter>();

                    foreach (var kvp in fields)
                    {
                        setClauses.Add($"[{kvp.Key}] = @{kvp.Key}");
                        parameters.Add(new SqlParameter($"@{kvp.Key}", kvp.Value ?? DBNull.Value));
                    }

                    string updateQuery = $@"
                        UPDATE [Pointage_DB].[dbo].[verif]
                        SET {string.Join(", ", setClauses)}
                        WHERE [employee_id] = @employeeId";

                    using (SqlCommand updateCommand = new SqlCommand(updateQuery, connection, transaction))
                    {
                        updateCommand.Parameters.AddRange(parameters.ToArray());
                        updateCommand.Parameters.AddWithValue("@employeeId", employeeId);

                        await updateCommand.ExecuteNonQueryAsync();
                    }
                }
                else
                {
                    // Insertion
                    var columnNames = new List<string> { "employee_id" };
                    var valueNames = new List<string> { "@employeeId" };
                    var parameters = new List<SqlParameter> { new SqlParameter("@employeeId", employeeId) };

                    foreach (var kvp in fields)
                    {
                        columnNames.Add($"[{kvp.Key}]");
                        valueNames.Add($"@{kvp.Key}");
                        parameters.Add(new SqlParameter($"@{kvp.Key}", kvp.Value ?? DBNull.Value));
                    }

                    string insertQuery = $@"
                        INSERT INTO [Pointage_DB].[dbo].[verif]
                        ({string.Join(", ", columnNames)})
                        VALUES ({string.Join(", ", valueNames)})";

                    using (SqlCommand insertCommand = new SqlCommand(insertQuery, connection, transaction))
                    {
                        insertCommand.Parameters.AddRange(parameters.ToArray());
                        await insertCommand.ExecuteNonQueryAsync();
                    }
                }
            }
        }

        /// <summary>
        /// Crée dynamiquement un nouvel enregistrement Employee-Verif en utilisant ExpandoObject
        /// Cette méthode s'adapte automatiquement aux changements de structure des tables
        /// </summary>
        [HttpPost("dynamic-create")]
        public async Task<IActionResult> CreateEmployeeVerifDynamic([FromBody] ExpandoObject createData)
        {
            try
            {
                var createDict = createData as IDictionary<string, object>;
                if (createDict == null || !createDict.Any())
                {
                    return BadRequest(new {
                        message = "Aucune donnée fournie pour la création.",
                        timestamp = DateTime.UtcNow
                    });
                }

                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlTransaction transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Séparer les champs Employee et Verif
                            var employeeFields = new Dictionary<string, object>();
                            var verifFields = new Dictionary<string, object>();

                            // Obtenir les colonnes des tables pour la séparation
                            var employeeColumns = await GetTableColumnsFromDb(connection, transaction, "employee");
                            var verifColumns = await GetTableColumnsFromDb(connection, transaction, "verif");

                            foreach (var kvp in createDict)
                            {
                                string columnName = kvp.Key;

                                // Nettoyer le nom de colonne (enlever le préfixe Verif si présent)
                                string cleanColumnName = columnName.StartsWith("Verif") ? columnName.Substring(5) : columnName;

                                if (employeeColumns.Contains(columnName, StringComparer.OrdinalIgnoreCase))
                                {
                                    employeeFields[columnName] = kvp.Value;
                                }
                                else if (verifColumns.Contains(cleanColumnName, StringComparer.OrdinalIgnoreCase))
                                {
                                    verifFields[cleanColumnName] = kvp.Value;
                                }
                                else if (columnName.StartsWith("Verif") && verifColumns.Contains(cleanColumnName, StringComparer.OrdinalIgnoreCase))
                                {
                                    verifFields[cleanColumnName] = kvp.Value;
                                }
                            }

                            // Validation : au moins des données Employee sont requises
                            if (!employeeFields.Any())
                            {
                                return BadRequest(new {
                                    message = "Aucune donnée Employee valide fournie.",
                                    availableEmployeeColumns = employeeColumns,
                                    timestamp = DateTime.UtcNow
                                });
                            }

                            // Créer l'employé et récupérer l'ID
                            int newEmployeeId = await CreateEmployeeDynamic(connection, transaction, employeeFields);

                            // Créer les données de vérification si fournies
                            if (verifFields.Any())
                            {
                                await CreateVerifDynamic(connection, transaction, newEmployeeId, verifFields);
                            }

                            // Valider la transaction
                            await transaction.CommitAsync();

                            // Récupérer les données créées pour la réponse
                            var createdData = await GetEmployeeVerifByIdDynamic(connection, newEmployeeId);

                            return Ok(new
                            {
                                message = "Enregistrement Employee-Verif créé avec succès",
                                employeeId = newEmployeeId,
                                data = createdData,
                                fieldsProcessed = new
                                {
                                    employeeFields = employeeFields.Keys.ToList(),
                                    verifFields = verifFields.Keys.ToList()
                                },
                                timestamp = DateTime.UtcNow
                            });
                        }
                        catch (Exception ex)
                        {
                            await transaction.RollbackAsync();
                            throw new Exception($"Erreur lors de la création: {ex.Message}", ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erreur interne du serveur lors de la création",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Récupère tous les enregistrements Employee-Verif de manière dynamique avec ExpandoObject
        /// Cette méthode s'adapte automatiquement aux changements de structure des tables
        /// </summary>
        [HttpGet("dynamic-getall")]
        public async Task<IActionResult> GetAllEmployeeVerifDynamic(
            [FromQuery] int? page = 1,
            [FromQuery] int? pageSize = 50,
            [FromQuery] string? sortBy = "idE",
            [FromQuery] string? sortOrder = "ASC",
            [FromQuery] string? filter = null)
        {
            try
            {
                // Validation des paramètres
                page = Math.Max(1, page ?? 1);
                pageSize = Math.Max(1, Math.Min(1000, pageSize ?? 50)); // Limite à 1000 pour éviter les surcharges
                sortOrder = (sortOrder?.ToUpper() == "DESC") ? "DESC" : "ASC";

                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // Construire la requête dynamique avec pagination
                    string baseQuery = @"
                        SELECT e.*, v.*
                        FROM [Pointage_DB].[dbo].[employee] e
                        LEFT JOIN [Pointage_DB].[dbo].[verif] v ON e.[idE] = v.[employee_id]";

                    // Ajouter le filtre si fourni
                    var parameters = new List<SqlParameter>();
                    if (!string.IsNullOrWhiteSpace(filter))
                    {
                        baseQuery += @"
                            WHERE (e.[Nom] LIKE @filter
                                OR e.[Prenom] LIKE @filter
                                OR e.[Email] LIKE @filter
                                OR e.[Matricule] LIKE @filter)";
                        parameters.Add(new SqlParameter("@filter", $"%{filter}%"));
                    }

                    // Ajouter l'ordre et la pagination
                    string orderByClause = $"ORDER BY e.[{sortBy}] {sortOrder}";
                    int offset = (page.Value - 1) * pageSize.Value;

                    string paginatedQuery = $@"
                        {baseQuery}
                        {orderByClause}
                        OFFSET @offset ROWS
                        FETCH NEXT @pageSize ROWS ONLY";

                    parameters.Add(new SqlParameter("@offset", offset));
                    parameters.Add(new SqlParameter("@pageSize", pageSize.Value));

                    // Requête pour le nombre total d'enregistrements
                    string countQuery = $@"
                        SELECT COUNT(*)
                        FROM [Pointage_DB].[dbo].[employee] e
                        LEFT JOIN [Pointage_DB].[dbo].[verif] v ON e.[idE] = v.[employee_id]";

                    if (!string.IsNullOrWhiteSpace(filter))
                    {
                        countQuery += @"
                            WHERE (e.[Nom] LIKE @filter
                                OR e.[Prenom] LIKE @filter
                                OR e.[Email] LIKE @filter
                                OR e.[Matricule] LIKE @filter)";
                    }

                    // Exécuter la requête de comptage
                    int totalCount = 0;
                    using (SqlCommand countCommand = new SqlCommand(countQuery, connection))
                    {
                        if (!string.IsNullOrWhiteSpace(filter))
                        {
                            countCommand.Parameters.Add(new SqlParameter("@filter", $"%{filter}%"));
                        }
                        var countResult = await countCommand.ExecuteScalarAsync();
                        totalCount = countResult != null ? Convert.ToInt32(countResult) : 0;
                    }

                    // Exécuter la requête principale
                    var employeeVerifList = new List<ExpandoObject>();
                    using (SqlCommand command = new SqlCommand(paginatedQuery, connection))
                    {
                        command.Parameters.AddRange(parameters.ToArray());
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var employeeVerif = new ExpandoObject() as IDictionary<string, object>;

                                // Lire toutes les colonnes dynamiquement
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    string columnName = reader.GetName(i);
                                    object? value = reader.IsDBNull(i) ? null : reader.GetValue(i);

                                    // Éviter les doublons de colonnes (employee_id peut apparaître deux fois)
                                    if (!employeeVerif.ContainsKey(columnName))
                                    {
                                        employeeVerif[columnName] = value ?? DBNull.Value;
                                    }
                                }

                                // Ajouter les propriétés calculées
                                AddCalculatedProperties(employeeVerif);

                                employeeVerifList.Add((ExpandoObject)employeeVerif!);
                            }
                        }
                    }

                    // Obtenir les métadonnées des tables
                    var metadata = await GetTableMetadata();

                    // Calculer les informations de pagination
                    int totalPages = (int)Math.Ceiling((double)totalCount / pageSize.Value);
                    bool hasNextPage = page.Value < totalPages;
                    bool hasPreviousPage = page.Value > 1;

                    return Ok(new
                    {
                        Data = employeeVerifList,
                        Pagination = new
                        {
                            CurrentPage = page.Value,
                            PageSize = pageSize.Value,
                            TotalCount = totalCount,
                            TotalPages = totalPages,
                            HasNextPage = hasNextPage,
                            HasPreviousPage = hasPreviousPage
                        },
                        Filters = new
                        {
                            SortBy = sortBy,
                            SortOrder = sortOrder,
                            Filter = filter
                        },
                        Metadata = metadata,
                        Timestamp = DateTime.UtcNow
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erreur interne du serveur lors de la récupération des données",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Crée dynamiquement un nouvel employé et retourne son ID
        /// </summary>
        private async Task<int> CreateEmployeeDynamic(SqlConnection connection, SqlTransaction transaction, Dictionary<string, object> fields)
        {
            if (!fields.Any())
                throw new ArgumentException("Aucun champ fourni pour la création de l'employé");

            var columnNames = new List<string>();
            var valueNames = new List<string>();
            var parameters = new List<SqlParameter>();

            foreach (var kvp in fields)
            {
                // Ignorer l'ID car il est auto-généré
                if (kvp.Key.Equals("idE", StringComparison.OrdinalIgnoreCase))
                    continue;

                columnNames.Add($"[{kvp.Key}]");
                valueNames.Add($"@{kvp.Key}");
                parameters.Add(new SqlParameter($"@{kvp.Key}", kvp.Value ?? DBNull.Value));
            }

            if (!columnNames.Any())
                throw new ArgumentException("Aucun champ valide fourni pour la création de l'employé");

            string insertQuery = $@"
                INSERT INTO [Pointage_DB].[dbo].[employee]
                ({string.Join(", ", columnNames)})
                OUTPUT INSERTED.idE
                VALUES ({string.Join(", ", valueNames)})";

            using (SqlCommand command = new SqlCommand(insertQuery, connection, transaction))
            {
                command.Parameters.AddRange(parameters.ToArray());
                var result = await command.ExecuteScalarAsync();
                return result != null ? Convert.ToInt32(result) : 0;
            }
        }

        /// <summary>
        /// Crée dynamiquement un enregistrement de vérification
        /// </summary>
        private async Task CreateVerifDynamic(SqlConnection connection, SqlTransaction transaction, int employeeId, Dictionary<string, object> fields)
        {
            if (!fields.Any()) return;

            var columnNames = new List<string> { "employee_id" };
            var valueNames = new List<string> { "@employeeId" };
            var parameters = new List<SqlParameter> { new SqlParameter("@employeeId", employeeId) };

            foreach (var kvp in fields)
            {
                // Ignorer l'ID de vérification car il est auto-généré
                if (kvp.Key.Equals("idV", StringComparison.OrdinalIgnoreCase) ||
                    kvp.Key.Equals("employee_id", StringComparison.OrdinalIgnoreCase))
                    continue;

                columnNames.Add($"[{kvp.Key}]");
                valueNames.Add($"@{kvp.Key}");
                parameters.Add(new SqlParameter($"@{kvp.Key}", kvp.Value ?? DBNull.Value));
            }

            string insertQuery = $@"
                INSERT INTO [Pointage_DB].[dbo].[verif]
                ({string.Join(", ", columnNames)})
                VALUES ({string.Join(", ", valueNames)})";

            using (SqlCommand command = new SqlCommand(insertQuery, connection, transaction))
            {
                command.Parameters.AddRange(parameters.ToArray());
                await command.ExecuteNonQueryAsync();
            }
        }

        /// <summary>
        /// Récupère un enregistrement Employee-Verif par ID de manière dynamique
        /// </summary>
        private async Task<ExpandoObject> GetEmployeeVerifByIdDynamic(SqlConnection connection, int employeeId)
        {
            string query = @"
                SELECT e.*, v.*
                FROM [Pointage_DB].[dbo].[employee] e
                LEFT JOIN [Pointage_DB].[dbo].[verif] v ON e.[idE] = v.[employee_id]
                WHERE e.[idE] = @employeeId";

            using (SqlCommand command = new SqlCommand(query, connection))
            {
                command.Parameters.AddWithValue("@employeeId", employeeId);
                using (SqlDataReader reader = await command.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        var employeeVerif = new ExpandoObject() as IDictionary<string, object>;

                        // Lire toutes les colonnes dynamiquement
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            string columnName = reader.GetName(i);
                            object? value = reader.IsDBNull(i) ? null : reader.GetValue(i);

                            // Éviter les doublons de colonnes
                            if (!employeeVerif.ContainsKey(columnName))
                            {
                                employeeVerif[columnName] = value ?? DBNull.Value;
                            }
                        }

                        // Ajouter les propriétés calculées
                        AddCalculatedProperties(employeeVerif);

                        return (ExpandoObject)employeeVerif!;
                    }
                }
            }

            return new ExpandoObject();
        }

        #region CRUD Dynamique pour Conges

        /// <summary>
        /// Récupère tous les congés avec ExpandoObject dynamique
        /// </summary>
        [HttpGet("conges/dynamic-getall")]
        public async Task<IActionResult> GetAllCongesDynamic(
            [FromQuery] int? page = 1,
            [FromQuery] int? pageSize = 50,
            [FromQuery] string? sortBy = "Id",
            [FromQuery] string? sortOrder = "ASC",
            [FromQuery] string? filter = null)
        {
            try
            {
                // Validation des paramètres
                page = Math.Max(1, page ?? 1);
                pageSize = Math.Min(1000, Math.Max(1, pageSize ?? 50));
                sortOrder = (sortOrder?.ToUpper() == "DESC") ? "DESC" : "ASC";

                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Récupérer les colonnes de la table Conges
                var congesColumns = await GetTableColumnsFromDb(connection, null!, "Conges");

                // Valider la colonne de tri
                if (!string.IsNullOrEmpty(sortBy) && !congesColumns.Contains(sortBy, StringComparer.OrdinalIgnoreCase))
                {
                    sortBy = "Id";
                }

                // Construire la requête de base
                var baseQuery = "SELECT * FROM [Pointage_DB].[dbo].[Conges]";
                var whereClause = "";
                var parameters = new List<SqlParameter>();

                // Ajouter le filtrage si spécifié
                if (!string.IsNullOrEmpty(filter))
                {
                    whereClause = " WHERE (Motif LIKE @filter OR Statut LIKE @filter)";
                    parameters.Add(new SqlParameter("@filter", $"%{filter}%"));
                }

                // Compter le total
                var countQuery = $"SELECT COUNT(*) FROM [Pointage_DB].[dbo].[Conges]{whereClause}";
                var countCommand = new SqlCommand(countQuery, connection);
                foreach (var param in parameters)
                {
                    countCommand.Parameters.Add(new SqlParameter(param.ParameterName, param.Value));
                }
                var totalCount = (int)await countCommand.ExecuteScalarAsync();

                // Calculer la pagination
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize.Value);
                var offset = (page.Value - 1) * pageSize.Value;

                // Requête avec pagination
                var query = $@"{baseQuery}{whereClause}
                              ORDER BY [{sortBy}] {sortOrder}
                              OFFSET @offset ROWS
                              FETCH NEXT @pageSize ROWS ONLY";

                var command = new SqlCommand(query, connection);
                foreach (var param in parameters)
                {
                    command.Parameters.Add(new SqlParameter(param.ParameterName, param.Value));
                }
                command.Parameters.AddWithValue("@offset", offset);
                command.Parameters.AddWithValue("@pageSize", pageSize.Value);

                var conges = new List<ExpandoObject>();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    var conge = new ExpandoObject() as IDictionary<string, object>;

                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        var columnName = reader.GetName(i);
                        var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                        conge[columnName] = value;
                    }

                    // Ajouter des propriétés calculées
                    AddCongeCalculatedProperties(conge);
                    conges.Add((ExpandoObject)conge);
                }

                return Ok(new
                {
                    Data = conges,
                    Pagination = new
                    {
                        CurrentPage = page.Value,
                        PageSize = pageSize.Value,
                        TotalCount = totalCount,
                        TotalPages = totalPages,
                        HasNextPage = page.Value < totalPages,
                        HasPreviousPage = page.Value > 1
                    },
                    Filters = new
                    {
                        SortBy = sortBy,
                        SortOrder = sortOrder,
                        Filter = filter
                    },
                    Metadata = new
                    {
                        CongesTable = new
                        {
                            TableName = "Conges",
                            Columns = congesColumns
                        }
                    },
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erreur lors de la récupération des congés",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Récupère un congé spécifique par ID avec ExpandoObject
        /// </summary>
        [HttpGet("conges/dynamic-get/{id}")]
        public async Task<IActionResult> GetCongeDynamic(int id)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = "SELECT * FROM [Pointage_DB].[dbo].[Conges] WHERE Id = @id";
                var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@id", id);

                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    var conge = new ExpandoObject() as IDictionary<string, object>;

                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        var columnName = reader.GetName(i);
                        var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                        conge[columnName] = value;
                    }

                    AddCongeCalculatedProperties(conge);

                    return Ok(new
                    {
                        Data = (ExpandoObject)conge,
                        Timestamp = DateTime.UtcNow
                    });
                }

                return NotFound(new
                {
                    message = $"Congé avec l'ID {id} non trouvé",
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erreur lors de la récupération du congé",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Crée un nouveau congé de manière dynamique
        /// </summary>
        [HttpPost("conges/dynamic-create")]
        public async Task<IActionResult> CreateCongeDynamic([FromBody] ExpandoObject createData)
        {
            try
            {
                if (createData == null)
                {
                    return BadRequest(new
                    {
                        message = "Les données de création sont requises",
                        timestamp = DateTime.UtcNow
                    });
                }

                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                using var transaction = connection.BeginTransaction();

                try
                {
                    // Récupérer les colonnes de la table
                    var congesColumns = await GetTableColumnsFromDb(connection, transaction, "Conges");
                    var dataDict = createData as IDictionary<string, object>;

                    // Filtrer les champs valides (exclure Id car auto-généré)
                    var validFields = new Dictionary<string, object>();
                    foreach (var kvp in dataDict!)
                    {
                        if (congesColumns.Contains(kvp.Key, StringComparer.OrdinalIgnoreCase) &&
                            !kvp.Key.Equals("Id", StringComparison.OrdinalIgnoreCase))
                        {
                            validFields[kvp.Key] = kvp.Value;
                        }
                    }

                    if (!validFields.Any())
                    {
                        return BadRequest(new
                        {
                            message = "Aucun champ valide fourni pour la création",
                            availableColumns = congesColumns,
                            timestamp = DateTime.UtcNow
                        });
                    }

                    // Construire la requête d'insertion
                    var columns = string.Join(", ", validFields.Keys.Select(k => $"[{k}]"));
                    var values = string.Join(", ", validFields.Keys.Select(k => $"@{k}"));
                    var insertQuery = $@"INSERT INTO [Pointage_DB].[dbo].[Conges] ({columns})
                                        OUTPUT INSERTED.Id
                                        VALUES ({values})";

                    var command = new SqlCommand(insertQuery, connection, transaction);

                    // Ajouter les paramètres
                    foreach (var kvp in validFields)
                    {
                        command.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value ?? DBNull.Value);
                    }

                    var newId = (int)await command.ExecuteScalarAsync();

                    // Récupérer l'enregistrement créé
                    var selectQuery = "SELECT * FROM [Pointage_DB].[dbo].[Conges] WHERE Id = @id";
                    var selectCommand = new SqlCommand(selectQuery, connection, transaction);
                    selectCommand.Parameters.AddWithValue("@id", newId);

                    using var reader = await selectCommand.ExecuteReaderAsync();
                    ExpandoObject? createdConge = null;

                    if (await reader.ReadAsync())
                    {
                        var conge = new ExpandoObject() as IDictionary<string, object>;
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            var columnName = reader.GetName(i);
                            var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                            conge[columnName] = value ?? DBNull.Value;
                        }
                        AddCongeCalculatedProperties(conge);
                        createdConge = (ExpandoObject)conge;
                    }

                    await transaction.CommitAsync();

                    return Ok(new
                    {
                        message = "Congé créé avec succès",
                        congeId = newId,
                        data = createdConge,
                        fieldsProcessed = validFields.Keys.ToList(),
                        timestamp = DateTime.UtcNow
                    });
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erreur lors de la création du congé",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Met à jour un congé de manière dynamique
        /// </summary>
        [HttpPut("conges/dynamic-update/{id}")]
        public async Task<IActionResult> UpdateCongeDynamic(int id, [FromBody] ExpandoObject updateData)
        {
            try
            {
                if (updateData == null)
                {
                    return BadRequest(new
                    {
                        message = "Les données de mise à jour sont requises",
                        timestamp = DateTime.UtcNow
                    });
                }

                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                using var transaction = connection.BeginTransaction();

                try
                {
                    // Vérifier que l'enregistrement existe
                    var checkQuery = "SELECT COUNT(*) FROM [Pointage_DB].[dbo].[Conges] WHERE Id = @id";
                    var checkCommand = new SqlCommand(checkQuery, connection, transaction);
                    checkCommand.Parameters.AddWithValue("@id", id);
                    var exists = (int)await checkCommand.ExecuteScalarAsync() > 0;

                    if (!exists)
                    {
                        return NotFound(new
                        {
                            message = $"Congé avec l'ID {id} non trouvé",
                            timestamp = DateTime.UtcNow
                        });
                    }

                    // Récupérer les colonnes de la table
                    var congesColumns = await GetTableColumnsFromDb(connection, transaction, "Conges");
                    var dataDict = updateData as IDictionary<string, object>;

                    // Filtrer les champs valides (exclure Id)
                    var validFields = new Dictionary<string, object>();
                    foreach (var kvp in dataDict!)
                    {
                        if (congesColumns.Contains(kvp.Key, StringComparer.OrdinalIgnoreCase) &&
                            !kvp.Key.Equals("Id", StringComparison.OrdinalIgnoreCase))
                        {
                            validFields[kvp.Key] = kvp.Value;
                        }
                    }

                    if (!validFields.Any())
                    {
                        return BadRequest(new
                        {
                            message = "Aucun champ valide fourni pour la mise à jour",
                            availableColumns = congesColumns,
                            timestamp = DateTime.UtcNow
                        });
                    }

                    // Construire la requête de mise à jour
                    var setClause = string.Join(", ", validFields.Keys.Select(k => $"[{k}] = @{k}"));
                    var updateQuery = $"UPDATE [Pointage_DB].[dbo].[Conges] SET {setClause} WHERE Id = @id";

                    var command = new SqlCommand(updateQuery, connection, transaction);
                    command.Parameters.AddWithValue("@id", id);

                    // Ajouter les paramètres
                    foreach (var kvp in validFields)
                    {
                        command.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value ?? DBNull.Value);
                    }

                    await command.ExecuteNonQueryAsync();

                    // Récupérer l'enregistrement mis à jour
                    var selectQuery = "SELECT * FROM [Pointage_DB].[dbo].[Conges] WHERE Id = @id";
                    var selectCommand = new SqlCommand(selectQuery, connection, transaction);
                    selectCommand.Parameters.AddWithValue("@id", id);

                    using var reader = await selectCommand.ExecuteReaderAsync();
                    ExpandoObject? updatedConge = null;

                    if (await reader.ReadAsync())
                    {
                        var conge = new ExpandoObject() as IDictionary<string, object>;
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            var columnName = reader.GetName(i);
                            var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                            conge[columnName] = value ?? DBNull.Value;
                        }
                        AddCongeCalculatedProperties(conge);
                        updatedConge = (ExpandoObject)conge;
                    }

                    await transaction.CommitAsync();

                    return Ok(new
                    {
                        message = "Congé mis à jour avec succès",
                        congeId = id,
                        data = updatedConge,
                        fieldsUpdated = validFields.Keys.ToList(),
                        timestamp = DateTime.UtcNow
                    });
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erreur lors de la mise à jour du congé",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Supprime un congé
        /// </summary>
        [HttpDelete("conges/dynamic-delete/{id}")]
        public async Task<IActionResult> DeleteCongeDynamic(int id)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Vérifier que l'enregistrement existe
                var checkQuery = "SELECT COUNT(*) FROM [Pointage_DB].[dbo].[Conges] WHERE Id = @id";
                var checkCommand = new SqlCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@id", id);
                var exists = (int)await checkCommand.ExecuteScalarAsync() > 0;

                if (!exists)
                {
                    return NotFound(new
                    {
                        message = $"Congé avec l'ID {id} non trouvé",
                        timestamp = DateTime.UtcNow
                    });
                }

                // Supprimer l'enregistrement
                var deleteQuery = "DELETE FROM [Pointage_DB].[dbo].[Conges] WHERE Id = @id";
                var deleteCommand = new SqlCommand(deleteQuery, connection);
                deleteCommand.Parameters.AddWithValue("@id", id);

                await deleteCommand.ExecuteNonQueryAsync();

                return Ok(new
                {
                    message = "Congé supprimé avec succès",
                    congeId = id,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erreur lors de la suppression du congé",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        #endregion

        #region Helper Methods pour Conges

        /// <summary>
        /// Ajoute des propriétés calculées pour les congés
        /// </summary>
        private void AddCongeCalculatedProperties(IDictionary<string, object> conge)
        {
            // Calcul de la durée du congé
            if (conge.ContainsKey("DateDebut") && conge.ContainsKey("DateFin") &&
                conge["DateDebut"] != null && conge["DateFin"] != null)
            {
                if (DateTime.TryParse(conge["DateDebut"].ToString(), out DateTime dateDebut) &&
                    DateTime.TryParse(conge["DateFin"].ToString(), out DateTime dateFin))
                {
                    var duree = (dateFin - dateDebut).Days + 1;
                    conge["DureeJours"] = duree;
                    conge["DureeSemaines"] = Math.Round(duree / 7.0, 2);
                }
            }

            // Statut enrichi
            if (conge.ContainsKey("Statut"))
            {
                var statut = conge["Statut"]?.ToString();
                conge["StatutCouleur"] = GetCongeStatutCouleur(statut);
                conge["StatutDescription"] = GetCongeStatutDescription(statut);
            }

            // Vérification des dates
            if (conge.ContainsKey("DateDebut") && conge["DateDebut"] != null)
            {
                if (DateTime.TryParse(conge["DateDebut"].ToString(), out DateTime dateDebut))
                {
                    conge["EstFutur"] = dateDebut > DateTime.Today;
                    conge["EstEnCours"] = dateDebut <= DateTime.Today &&
                        (conge.ContainsKey("DateFin") && DateTime.TryParse(conge["DateFin"].ToString(), out DateTime dateFin) && dateFin >= DateTime.Today);
                    conge["EstPasse"] = dateDebut < DateTime.Today &&
                        (conge.ContainsKey("DateFin") && DateTime.TryParse(conge["DateFin"].ToString(), out DateTime dateFinPasse) && dateFinPasse < DateTime.Today);
                }
            }
        }

        private string GetCongeStatutCouleur(string? statut)
        {
            return statut?.ToLower() switch
            {
                "approuve" or "approuvé" => "#28a745", // Vert
                "en_attente" or "en attente" => "#ffc107", // Jaune
                "refuse" or "refusé" => "#dc3545", // Rouge
                "annule" or "annulé" => "#6c757d", // Gris
                _ => "#17a2b8" // Bleu par défaut
            };
        }

        private string GetCongeStatutDescription(string? statut)
        {
            return statut?.ToLower() switch
            {
                "approuve" or "approuvé" => "Congé approuvé et confirmé",
                "en_attente" or "en attente" => "En attente d'approbation",
                "refuse" or "refusé" => "Demande refusée",
                "annule" or "annulé" => "Congé annulé",
                _ => "Statut non défini"
            };
        }

        #endregion

        #region CRUD Dynamique pour Retards

        /// <summary>
        /// Récupère tous les retards avec ExpandoObject dynamique
        /// </summary>
        [HttpGet("retards/dynamic-getall")]
        public async Task<IActionResult> GetAllRetardsDynamic(
            [FromQuery] int? page = 1,
            [FromQuery] int? pageSize = 50,
            [FromQuery] string? sortBy = "Id",
            [FromQuery] string? sortOrder = "ASC",
            [FromQuery] string? filter = null)
        {
            try
            {
                // Validation des paramètres
                page = Math.Max(1, page ?? 1);
                pageSize = Math.Min(1000, Math.Max(1, pageSize ?? 50));
                sortOrder = (sortOrder?.ToUpper() == "DESC") ? "DESC" : "ASC";

                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Récupérer les colonnes de la table Retards
                var retardsColumns = await GetTableColumnsFromDb(connection, null!, "Retards");

                // Valider la colonne de tri
                if (!string.IsNullOrEmpty(sortBy) && !retardsColumns.Contains(sortBy, StringComparer.OrdinalIgnoreCase))
                {
                    sortBy = "Id";
                }

                // Construire la requête de base
                var baseQuery = "SELECT * FROM [Pointage_DB].[dbo].[Retards]";
                var whereClause = "";
                var parameters = new List<SqlParameter>();

                // Ajouter le filtrage si spécifié
                if (!string.IsNullOrEmpty(filter))
                {
                    whereClause = " WHERE (Commentaire LIKE @filter)";
                    parameters.Add(new SqlParameter("@filter", $"%{filter}%"));
                }

                // Compter le total
                var countQuery = $"SELECT COUNT(*) FROM [Pointage_DB].[dbo].[Retards]{whereClause}";
                var countCommand = new SqlCommand(countQuery, connection);
                foreach (var param in parameters)
                {
                    countCommand.Parameters.Add(new SqlParameter(param.ParameterName, param.Value));
                }
                var totalCount = (int)await countCommand.ExecuteScalarAsync();

                // Calculer la pagination
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize.Value);
                var offset = (page.Value - 1) * pageSize.Value;

                // Requête avec pagination
                var query = $@"{baseQuery}{whereClause}
                              ORDER BY [{sortBy}] {sortOrder}
                              OFFSET @offset ROWS
                              FETCH NEXT @pageSize ROWS ONLY";

                var command = new SqlCommand(query, connection);
                foreach (var param in parameters)
                {
                    command.Parameters.Add(new SqlParameter(param.ParameterName, param.Value));
                }
                command.Parameters.AddWithValue("@offset", offset);
                command.Parameters.AddWithValue("@pageSize", pageSize.Value);

                var retards = new List<ExpandoObject>();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    var retard = new ExpandoObject() as IDictionary<string, object>;

                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        var columnName = reader.GetName(i);
                        var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                        retard[columnName] = value ?? DBNull.Value;
                    }

                    // Ajouter des propriétés calculées
                    AddRetardCalculatedProperties(retard);
                    retards.Add((ExpandoObject)retard);
                }

                return Ok(new
                {
                    Data = retards,
                    Pagination = new
                    {
                        CurrentPage = page.Value,
                        PageSize = pageSize.Value,
                        TotalCount = totalCount,
                        TotalPages = totalPages,
                        HasNextPage = page.Value < totalPages,
                        HasPreviousPage = page.Value > 1
                    },
                    Filters = new
                    {
                        SortBy = sortBy,
                        SortOrder = sortOrder,
                        Filter = filter
                    },
                    Metadata = new
                    {
                        RetardsTable = new
                        {
                            TableName = "Retards",
                            Columns = retardsColumns
                        }
                    },
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erreur lors de la récupération des retards",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Récupère un retard spécifique par ID avec ExpandoObject
        /// </summary>
        [HttpGet("retards/dynamic-get/{id}")]
        public async Task<IActionResult> GetRetardDynamic(int id)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = "SELECT * FROM [Pointage_DB].[dbo].[Retards] WHERE Id = @id";
                var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@id", id);

                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    var retard = new ExpandoObject() as IDictionary<string, object>;

                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        var columnName = reader.GetName(i);
                        var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                        retard[columnName] = value ?? DBNull.Value;
                    }

                    AddRetardCalculatedProperties(retard);

                    return Ok(new
                    {
                        Data = (ExpandoObject)retard,
                        Timestamp = DateTime.UtcNow
                    });
                }

                return NotFound(new
                {
                    message = $"Retard avec l'ID {id} non trouvé",
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erreur lors de la récupération du retard",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Crée un nouveau retard de manière dynamique
        /// </summary>
        [HttpPost("retards/dynamic-create")]
        public async Task<IActionResult> CreateRetardDynamic([FromBody] ExpandoObject createData)
        {
            try
            {
                if (createData == null)
                {
                    return BadRequest(new
                    {
                        message = "Les données de création sont requises",
                        timestamp = DateTime.UtcNow
                    });
                }

                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                using var transaction = connection.BeginTransaction();

                try
                {
                    // Récupérer les colonnes de la table
                    var retardsColumns = await GetTableColumnsFromDb(connection, transaction, "Retards");
                    var dataDict = createData as IDictionary<string, object>;

                    // Filtrer les champs valides (exclure Id car auto-généré)
                    var validFields = new Dictionary<string, object>();
                    foreach (var kvp in dataDict!)
                    {
                        if (retardsColumns.Contains(kvp.Key, StringComparer.OrdinalIgnoreCase) &&
                            !kvp.Key.Equals("Id", StringComparison.OrdinalIgnoreCase))
                        {
                            validFields[kvp.Key] = kvp.Value;
                        }
                    }

                    if (!validFields.Any())
                    {
                        return BadRequest(new
                        {
                            message = "Aucun champ valide fourni pour la création",
                            availableColumns = retardsColumns,
                            timestamp = DateTime.UtcNow
                        });
                    }

                    // Construire la requête d'insertion
                    var columns = string.Join(", ", validFields.Keys.Select(k => $"[{k}]"));
                    var values = string.Join(", ", validFields.Keys.Select(k => $"@{k}"));
                    var insertQuery = $@"INSERT INTO [Pointage_DB].[dbo].[Retards] ({columns})
                                        OUTPUT INSERTED.Id
                                        VALUES ({values})";

                    var command = new SqlCommand(insertQuery, connection, transaction);

                    // Ajouter les paramètres
                    foreach (var kvp in validFields)
                    {
                        command.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value ?? DBNull.Value);
                    }

                    var newId = (int)await command.ExecuteScalarAsync();

                    // Récupérer l'enregistrement créé
                    var selectQuery = "SELECT * FROM [Pointage_DB].[dbo].[Retards] WHERE Id = @id";
                    var selectCommand = new SqlCommand(selectQuery, connection, transaction);
                    selectCommand.Parameters.AddWithValue("@id", newId);

                    using var reader = await selectCommand.ExecuteReaderAsync();
                    ExpandoObject? createdRetard = null;

                    if (await reader.ReadAsync())
                    {
                        var retard = new ExpandoObject() as IDictionary<string, object>;
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            var columnName = reader.GetName(i);
                            var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                            retard[columnName] = value ?? DBNull.Value;
                        }
                        AddRetardCalculatedProperties(retard);
                        createdRetard = (ExpandoObject)retard;
                    }

                    await transaction.CommitAsync();

                    return Ok(new
                    {
                        message = "Retard créé avec succès",
                        retardId = newId,
                        data = createdRetard,
                        fieldsProcessed = validFields.Keys.ToList(),
                        timestamp = DateTime.UtcNow
                    });
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erreur lors de la création du retard",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Met à jour un retard de manière dynamique
        /// </summary>
        [HttpPut("retards/dynamic-update/{id}")]
        public async Task<IActionResult> UpdateRetardDynamic(int id, [FromBody] ExpandoObject updateData)
        {
            try
            {
                if (updateData == null)
                {
                    return BadRequest(new
                    {
                        message = "Les données de mise à jour sont requises",
                        timestamp = DateTime.UtcNow
                    });
                }

                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                using var transaction = connection.BeginTransaction();

                try
                {
                    // Vérifier que l'enregistrement existe
                    var checkQuery = "SELECT COUNT(*) FROM [Pointage_DB].[dbo].[Retards] WHERE Id = @id";
                    var checkCommand = new SqlCommand(checkQuery, connection, transaction);
                    checkCommand.Parameters.AddWithValue("@id", id);
                    var exists = (int)await checkCommand.ExecuteScalarAsync() > 0;

                    if (!exists)
                    {
                        return NotFound(new
                        {
                            message = $"Retard avec l'ID {id} non trouvé",
                            timestamp = DateTime.UtcNow
                        });
                    }

                    // Récupérer les colonnes de la table
                    var retardsColumns = await GetTableColumnsFromDb(connection, transaction, "Retards");
                    var dataDict = updateData as IDictionary<string, object>;

                    // Filtrer les champs valides (exclure Id)
                    var validFields = new Dictionary<string, object>();
                    foreach (var kvp in dataDict!)
                    {
                        if (retardsColumns.Contains(kvp.Key, StringComparer.OrdinalIgnoreCase) &&
                            !kvp.Key.Equals("Id", StringComparison.OrdinalIgnoreCase))
                        {
                            validFields[kvp.Key] = kvp.Value;
                        }
                    }

                    if (!validFields.Any())
                    {
                        return BadRequest(new
                        {
                            message = "Aucun champ valide fourni pour la mise à jour",
                            availableColumns = retardsColumns,
                            timestamp = DateTime.UtcNow
                        });
                    }

                    // Construire la requête de mise à jour
                    var setClause = string.Join(", ", validFields.Keys.Select(k => $"[{k}] = @{k}"));
                    var updateQuery = $"UPDATE [Pointage_DB].[dbo].[Retards] SET {setClause} WHERE Id = @id";

                    var command = new SqlCommand(updateQuery, connection, transaction);
                    command.Parameters.AddWithValue("@id", id);

                    // Ajouter les paramètres
                    foreach (var kvp in validFields)
                    {
                        command.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value ?? DBNull.Value);
                    }

                    await command.ExecuteNonQueryAsync();

                    // Récupérer l'enregistrement mis à jour
                    var selectQuery = "SELECT * FROM [Pointage_DB].[dbo].[Retards] WHERE Id = @id";
                    var selectCommand = new SqlCommand(selectQuery, connection, transaction);
                    selectCommand.Parameters.AddWithValue("@id", id);

                    using var reader = await selectCommand.ExecuteReaderAsync();
                    ExpandoObject? updatedRetard = null;

                    if (await reader.ReadAsync())
                    {
                        var retard = new ExpandoObject() as IDictionary<string, object>;
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            var columnName = reader.GetName(i);
                            var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                            retard[columnName] = value ?? DBNull.Value;
                        }
                        AddRetardCalculatedProperties(retard);
                        updatedRetard = (ExpandoObject)retard;
                    }

                    await transaction.CommitAsync();

                    return Ok(new
                    {
                        message = "Retard mis à jour avec succès",
                        retardId = id,
                        data = updatedRetard,
                        fieldsUpdated = validFields.Keys.ToList(),
                        timestamp = DateTime.UtcNow
                    });
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erreur lors de la mise à jour du retard",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Supprime un retard
        /// </summary>
        [HttpDelete("retards/dynamic-delete/{id}")]
        public async Task<IActionResult> DeleteRetardDynamic(int id)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Vérifier que l'enregistrement existe
                var checkQuery = "SELECT COUNT(*) FROM [Pointage_DB].[dbo].[Retards] WHERE Id = @id";
                var checkCommand = new SqlCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@id", id);
                var exists = (int)await checkCommand.ExecuteScalarAsync() > 0;

                if (!exists)
                {
                    return NotFound(new
                    {
                        message = $"Retard avec l'ID {id} non trouvé",
                        timestamp = DateTime.UtcNow
                    });
                }

                // Supprimer l'enregistrement
                var deleteQuery = "DELETE FROM [Pointage_DB].[dbo].[Retards] WHERE Id = @id";
                var deleteCommand = new SqlCommand(deleteQuery, connection);
                deleteCommand.Parameters.AddWithValue("@id", id);

                await deleteCommand.ExecuteNonQueryAsync();

                return Ok(new
                {
                    message = "Retard supprimé avec succès",
                    retardId = id,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erreur lors de la suppression du retard",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        #endregion

        #region Helper Methods pour Retards

        /// <summary>
        /// Ajoute des propriétés calculées pour les retards
        /// </summary>
        private void AddRetardCalculatedProperties(IDictionary<string, object> retard)
        {
            // Conversion des minutes en heures et minutes
            if (retard.ContainsKey("MinutesRetard") && retard["MinutesRetard"] != null)
            {
                if (int.TryParse(retard["MinutesRetard"].ToString(), out int minutes))
                {
                    var heures = minutes / 60;
                    var minutesRestantes = minutes % 60;
                    retard["DureeFormatee"] = $"{heures}h{minutesRestantes:D2}";
                    retard["HeuresRetard"] = Math.Round(minutes / 60.0, 2);
                }
            }

            // Statut du retard basé sur la justification
            if (retard.ContainsKey("Justifie"))
            {
                var justifie = retard["Justifie"];
                if (justifie is bool boolValue)
                {
                    retard["StatutRetard"] = boolValue ? "Justifié" : "Non justifié";
                    retard["StatutCouleur"] = boolValue ? "#28a745" : "#dc3545"; // Vert ou Rouge
                }
                else
                {
                    retard["StatutRetard"] = "Non défini";
                    retard["StatutCouleur"] = "#6c757d"; // Gris
                }
            }

            // Gravité du retard basée sur les minutes
            if (retard.ContainsKey("MinutesRetard") && retard["MinutesRetard"] != null)
            {
                if (int.TryParse(retard["MinutesRetard"].ToString(), out int minutes))
                {
                    retard["GraviteRetard"] = GetGraviteRetard(minutes);
                    retard["GraviteCouleur"] = GetGraviteRetardCouleur(minutes);
                }
            }
        }

        private string GetGraviteRetard(int minutes)
        {
            return minutes switch
            {
                <= 5 => "Léger",
                <= 15 => "Modéré",
                <= 30 => "Important",
                <= 60 => "Grave",
                _ => "Très grave"
            };
        }

        private string GetGraviteRetardCouleur(int minutes)
        {
            return minutes switch
            {
                <= 5 => "#28a745",   // Vert
                <= 15 => "#ffc107",  // Jaune
                <= 30 => "#fd7e14",  // Orange
                <= 60 => "#dc3545",  // Rouge
                _ => "#6f42c1"       // Violet
            };
        }

        #endregion
    }

    public class EmployeeDto
    {
        public string? Nom { get; set; }
        public string? Prenom { get; set; }
        public string? Matricule { get; set; }
        public string? Email { get; set; }
        public string? Poste { get; set; }
        public string? Statut { get; set; }
        public int? RoleId { get; set; }
        public string? Telephone { get; set; }
        public string? Adresse { get; set; }
        public string? Ville { get; set; }
        public string? Projet { get; set; }
        public DateTime? DateNaissance { get; set; }
        public int? TypeContratId { get; set; }
        public int? Age { get; set; }
        public int? PeriodeId { get; set; }
        public string? Code { get; set; }
        public string? Password { get; set; }
        public string? Rfid { get; set; }
        public string? PhotoFA { get; set; }
        public string? PhotoEM { get; set; }
        public string? PhotoPoi { get; set; }
        public string? Username { get; set; }
        public int? DepartementId { get; set; }
    }
}