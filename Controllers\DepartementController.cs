using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Data.SqlClient;
using Modal;

namespace Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DepartementController : ControllerBase
    {
        private readonly string _connectionString = "Server=DESKTOP-JM9HQ35\\MSSQLSERVER01;Database=Pointage_DB;Trusted_Connection=True;";

        [HttpGet]
        public IActionResult GetAll()
        {
            try
            {
                var departements = new List<DepartementModel>();
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("SELECT [id], [nom] FROM [Pointage_DB].[dbo].[departement]", connection);
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            departements.Add(new DepartementModel
                            {
                                id = reader.GetInt32(0),
                                nom = reader.IsDBNull(1) ? null : reader.GetString(1)
                            });
                        }
                    }
                }
                return Ok(departements);
            }
            catch (System.Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }
    }
}
