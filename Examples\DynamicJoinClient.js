/**
 * Exemple d'utilisation côté client pour la méthode dynamic-join
 * Démontre comment interagir avec l'API Employee-Verif de manière dynamique
 */

class EmployeeVerifDynamicClient {
    constructor(baseUrl = '/api/EmployeeVerif') {
        this.baseUrl = baseUrl;
    }

    /**
     * Récupère tous les employés avec leurs données de vérification
     */
    async getAllEmployeesWithVerif() {
        try {
            const response = await fetch(`${this.baseUrl}/dynamic-join`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            
            console.log('Données récupérées:', data);
            console.log('Nombre d\'employés:', data.Count);
            console.log('Métadonnées des tables:', data.Metadata);
            
            return data;
        } catch (error) {
            console.error('Erreur lors de la récupération des employés:', error);
            throw error;
        }
    }

    /**
     * Récupère un employé spécifique avec ses données de vérification
     */
    async getEmployeeWithVerif(employeeId) {
        try {
            const response = await fetch(`${this.baseUrl}/dynamic-join?employeeId=${employeeId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            
            if (data.Data && data.Data.length > 0) {
                console.log('Employé trouvé:', data.Data[0]);
                return data.Data[0];
            } else {
                console.log('Aucun employé trouvé avec l\'ID:', employeeId);
                return null;
            }
        } catch (error) {
            console.error('Erreur lors de la récupération de l\'employé:', error);
            throw error;
        }
    }

    /**
     * Récupère la structure des colonnes d'une table
     */
    async getTableStructure(tableName) {
        try {
            const response = await fetch(`${this.baseUrl}/dynamic-columns/${tableName}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            
            console.log(`Structure de la table ${tableName}:`, data);
            console.log('Colonnes:', data.Columns);
            
            return data;
        } catch (error) {
            console.error('Erreur lors de la récupération de la structure:', error);
            throw error;
        }
    }

    /**
     * Met à jour un employé de manière dynamique
     */
    async updateEmployeeDynamic(employeeId, updateData) {
        try {
            const response = await fetch(`${this.baseUrl}/dynamic-update/${employeeId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('Mise à jour réussie:', result);
            
            return result;
        } catch (error) {
            console.error('Erreur lors de la mise à jour:', error);
            throw error;
        }
    }

    /**
     * Crée un nouvel employé de manière dynamique
     */
    async createEmployeeDynamic(createData) {
        try {
            const response = await fetch(`${this.baseUrl}/dynamic-create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(createData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('Création réussie:', result);
            console.log('Nouvel ID employé:', result.employeeId);

            return result;
        } catch (error) {
            console.error('Erreur lors de la création:', error);
            throw error;
        }
    }

    /**
     * Récupère tous les employés avec pagination et filtrage
     */
    async getAllEmployeesWithPagination(options = {}) {
        try {
            const params = new URLSearchParams();

            if (options.page) params.append('page', options.page);
            if (options.pageSize) params.append('pageSize', options.pageSize);
            if (options.sortBy) params.append('sortBy', options.sortBy);
            if (options.sortOrder) params.append('sortOrder', options.sortOrder);
            if (options.filter) params.append('filter', options.filter);

            const url = `${this.baseUrl}/dynamic-getall${params.toString() ? '?' + params.toString() : ''}`;
            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Données paginées récupérées:', data);
            console.log('Page courante:', data.Pagination.CurrentPage);
            console.log('Total d\'employés:', data.Pagination.TotalCount);

            return data;
        } catch (error) {
            console.error('Erreur lors de la récupération paginée:', error);
            throw error;
        }
    }

    /**
     * Affiche les données d'un employé de manière formatée
     */
    displayEmployee(employee) {
        console.log('=== INFORMATIONS EMPLOYÉ ===');
        console.log(`Nom complet: ${employee.NomComplet || 'N/A'}`);
        console.log(`Matricule: ${employee.Matricule || 'N/A'}`);
        console.log(`Email: ${employee.Email || 'N/A'}`);
        console.log(`Poste: ${employee.Poste || 'N/A'}`);
        console.log(`Statut: ${employee.Statut || 'N/A'}`);
        
        console.log('\n=== DONNÉES DE VÉRIFICATION ===');
        console.log(`Statut de vérification: ${employee.VerificationStatus || 'N/A'}`);
        console.log(`Niveau de sécurité: ${employee.SecurityLevel || 'N/A'}`);
        console.log(`Méthodes d'authentification: ${employee.AuthenticationMethods ? employee.AuthenticationMethods.join(', ') : 'Aucune'}`);
        
        if (employee.VerifCode) console.log(`Code de vérification: ${employee.VerifCode}`);
        if (employee.VerifRfid) console.log(`RFID: ${employee.VerifRfid}`);
        
        console.log('\n=== PROPRIÉTÉS CALCULÉES ===');
        console.log(`Âge calculé: ${employee.AgeCalcule || 'N/A'}`);
        console.log(`Données de vérification: ${employee.HasVerificationData ? 'Oui' : 'Non'}`);
    }

    /**
     * Génère un tableau HTML dynamique basé sur les données
     */
    generateDynamicTable(employees, containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('Container non trouvé:', containerId);
            return;
        }

        if (!employees || employees.length === 0) {
            container.innerHTML = '<p>Aucun employé trouvé.</p>';
            return;
        }

        // Récupérer toutes les propriétés disponibles
        const allProperties = new Set();
        employees.forEach(emp => {
            Object.keys(emp).forEach(key => allProperties.add(key));
        });

        // Créer le tableau
        let html = '<table class="table table-striped table-hover">';
        
        // En-têtes
        html += '<thead class="table-dark"><tr>';
        allProperties.forEach(prop => {
            html += `<th>${prop}</th>`;
        });
        html += '</tr></thead>';

        // Corps du tableau
        html += '<tbody>';
        employees.forEach(emp => {
            html += '<tr>';
            allProperties.forEach(prop => {
                let value = emp[prop];
                
                // Formatage spécial pour certains types
                if (Array.isArray(value)) {
                    value = value.join(', ');
                } else if (value === null || value === undefined) {
                    value = '';
                } else if (typeof value === 'boolean') {
                    value = value ? 'Oui' : 'Non';
                }
                
                html += `<td>${value}</td>`;
            });
            html += '</tr>';
        });
        html += '</tbody></table>';

        container.innerHTML = html;
    }

    /**
     * Exemple d'utilisation complète
     */
    async demonstrateUsage() {
        console.log('=== DÉMONSTRATION DE L\'API DYNAMIC-JOIN ===\n');

        try {
            // 1. Récupérer tous les employés
            console.log('1. Récupération de tous les employés...');
            const allEmployees = await this.getAllEmployeesWithVerif();
            
            if (allEmployees.Data && allEmployees.Data.length > 0) {
                console.log(`Trouvé ${allEmployees.Count} employé(s)`);
                
                // Afficher le premier employé
                console.log('\n2. Affichage du premier employé:');
                this.displayEmployee(allEmployees.Data[0]);
                
                // 3. Récupérer la structure des tables
                console.log('\n3. Structure de la table Employee:');
                await this.getTableStructure('employee');
                
                console.log('\n4. Structure de la table Verif:');
                await this.getTableStructure('verif');
                
                // 4. Exemple de mise à jour
                console.log('\n5. Exemple de mise à jour dynamique:');
                const updateData = {
                    Email: '<EMAIL>',
                    VerifCode: 'NEWCODE123'
                };
                
                console.log('Données à mettre à jour:', updateData);
                // Note: Décommentez la ligne suivante pour effectuer la mise à jour
                // await this.updateEmployeeDynamic(allEmployees.Data[0].idE, updateData);
                
            } else {
                console.log('Aucun employé trouvé dans la base de données.');
            }
            
        } catch (error) {
            console.error('Erreur lors de la démonstration:', error);
        }
    }
}

// Exemple d'utilisation
document.addEventListener('DOMContentLoaded', async () => {
    const client = new EmployeeVerifDynamicClient();
    
    // Bouton pour charger tous les employés
    const loadAllBtn = document.getElementById('loadAllEmployees');
    if (loadAllBtn) {
        loadAllBtn.addEventListener('click', async () => {
            try {
                const data = await client.getAllEmployeesWithVerif();
                client.generateDynamicTable(data.Data, 'employeesTable');
            } catch (error) {
                console.error('Erreur:', error);
            }
        });
    }

    // Bouton pour la démonstration complète
    const demoBtn = document.getElementById('runDemo');
    if (demoBtn) {
        demoBtn.addEventListener('click', () => {
            client.demonstrateUsage();
        });
    }

    // Formulaire de mise à jour
    const updateForm = document.getElementById('updateForm');
    if (updateForm) {
        updateForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(updateForm);
            const employeeId = formData.get('employeeId');
            const updateData = {};
            
            // Construire l'objet de mise à jour dynamiquement
            for (let [key, value] of formData.entries()) {
                if (key !== 'employeeId' && value.trim() !== '') {
                    updateData[key] = value;
                }
            }
            
            try {
                await client.updateEmployeeDynamic(employeeId, updateData);
                alert('Mise à jour réussie!');
            } catch (error) {
                alert('Erreur lors de la mise à jour: ' + error.message);
            }
        });
    }
});

// Export pour utilisation en module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EmployeeVerifDynamicClient;
}
