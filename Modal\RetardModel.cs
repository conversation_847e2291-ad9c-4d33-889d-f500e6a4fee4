using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LearnAPI.Modal
{
    public class RetardModel
    {
        [Key]
        public int Id { get; set; }

        public int PointageId { get; set; }

        public int? MinutesRetard { get; set; }

        public bool? Justifie { get; set; }

        public string? Commentaire { get; set; }
    }
}
