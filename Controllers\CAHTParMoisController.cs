﻿using ClosedXML.Excel;
using LearnAPI.Container;
using LearnAPI.Modal;
using LearnAPI.Repos;
using LearnAPI.Service;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Data;
using LearnAPI.Helper;
using Microsoft.EntityFrameworkCore;
using FastReport.Data;
using Microsoft.Data.SqlClient; // <-- Utiliser ce namespace au lieu de System.Data.SqlClient

namespace LearnAPI.Controllers
{
    [ApiController]
    [Route("api/doc")]
    //[Route("api/[controller]")]
    public class CAHTParMoisController : ControllerBase
    {
        private readonly LearndataContext _context;

        public CAHTParMoisController(LearndataContext context)
        {
            _context = context;
        }

        [HttpGet("GetCA_HT_Par_Mois")]
        public async Task<IActionResult> GetCA_HT_Par_Mois()
        {
            try
            {
                
                // Modification de la requête SQL pour inclure le nouveau paramètre
                var results = await _context.CA_HT_Par_MoisResults
                    .FromSqlRaw("EXEC CA_HT_Par_Mois")
                    .ToListAsync();

                return Ok(results);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        //[HttpGet("GetListeDesDocumentsNonTransformer")]
        //public async Task<IActionResult> GetNonTransformedDocs(string docDest)
        //{
        //    try
        //    {
        //        var parameter = new SqlParameter("@DocDest", docDest);

        //        var results = await _context.DocNonTransformedResults
        //            .FromSqlRaw("EXEC Proc_Import_Doc @DocDest", parameter)
        //            .ToListAsync();

        //        return Ok(results);
        //    }
        //    catch (Exception ex)
        //    {
        //        return StatusCode(500, $"Internal server error: {ex.Message}");
        //    }
        //}
    }
}
