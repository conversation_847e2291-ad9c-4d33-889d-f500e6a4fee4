﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace LearnAPI.Modal
{
    [Keyless]
    public class Tvamodal
    {
        [Key]
        public int idt { get; set; } 

        [Required]
        [MaxLength(100)]
        public string nomtva { get; set; } 

        [Required]
        [Column(TypeName = "decimal(3, 0)")]
        [Range(0, 100)]
        public decimal? tauxtva { get; set; }   // Taux de TVA en pourcentage, 0, 7, 13,19.

        //public decimal? tauxtva { get; set; } // Taux de TVA en pourcentage, 0, 7, 13,19.
        // public int tauxtva { get; set; } // Taux de TVA en pourcentage, 0, 7, 13,19.

        [StringLength(50)]
        [Unicode(false)]
        public string Code { get; set; } = null!;

        [StringLength(50)]
        [Unicode(false)]
        public string Name { get; set; } = null!;

        public bool? IsActive { get; set; }

        public string? Statusname { get; set; }


    }
}
