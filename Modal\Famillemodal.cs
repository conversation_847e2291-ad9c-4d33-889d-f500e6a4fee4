﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace LearnAPI.Modal
{
    [Keyless]
    public class Famillemodal
    {
        [Key]
        public int idf { get; set; } 

        [Required]
        [MaxLength(100)]
        public string nomf { get; set; }

        [StringLength(50)]
        [Unicode(false)]
        public string Code { get; set; } = null!;

        public bool? IsActive { get; set; }

        public string? Statusname { get; set; }


    }
}
