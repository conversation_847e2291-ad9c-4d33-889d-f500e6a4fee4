﻿using LearnAPI.Helper;
using LearnAPI.Modal;


namespace LearnAPI.Service
{
    public interface ITvaService
    {
        Task<List<Tvamodal>> Getall();
        Task<Tvamodal> Getbycode(string code);
        Task<Tvamodal> GetById(int idt);
        Task<APIResponse> Remove(string code);
        Task<APIResponse> Create(Tvamodal data);

        Task<APIResponse> Update(Tvamodal data,string code);
    }
}
