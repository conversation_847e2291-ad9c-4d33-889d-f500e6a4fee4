﻿using AutoMapper;
using LearnAPI.Helper;
using LearnAPI.Modal;
using LearnAPI.Repos;
using LearnAPI.Repos.Models;
using LearnAPI.Service;
using Microsoft.EntityFrameworkCore;

namespace LearnAPI.Container
{
    public class FamilleService : IFamilleService
    {
        private readonly LearndataContext context;
        private readonly IMapper mapper;
        private readonly ILogger<FamilleService> logger;

        public FamilleService(LearndataContext context, IMapper mapper, ILogger<FamilleService> logger)
        {
            this.context = context;
            this.mapper = mapper;
            this.logger = logger;
        }
        
        public async Task<APIResponse> Create(Famillemodal data)
        {
            APIResponse response = new APIResponse();
            try
            {
                this.logger.LogInformation("Create Begins");
                TblFamille _famille = this.mapper.Map<Famillemodal, TblFamille>(data);
                await this.context.TblFamilles.AddAsync(_famille);
                await this.context.SaveChangesAsync();
                response.ResponseCode = 201;
                response.Result = "pass";
            }
            catch (Exception ex)
            {
                response.ResponseCode = 400;
                response.Message = ex.Message;
                this.logger.LogError(ex.Message, ex);
            }
            return response;
        }
        

        public async Task<List<Famillemodal>> Getall()
        {
            List<Famillemodal> _response = new List<Famillemodal>();
            var _data = await this.context.TblFamilles.ToListAsync();
            if (_data != null)
            {
                _response = this.mapper.Map<List<TblFamille>, List<Famillemodal>>(_data);
            }
            return _response;
        }

        public async Task<Famillemodal> Getbycode(string code)
        {
            Famillemodal _response = new Famillemodal();

            // Convert the string 'code' to an int before passing to FindAsync
            if (int.TryParse(code, out int parsedCode))
            {
                var _data = await this.context.TblFamilles.FindAsync(parsedCode);
                if (_data != null)
                {
                    _response = this.mapper.Map<TblFamille, Famillemodal>(_data);
                }
            }
            return _response;
        }

        public async Task<Famillemodal> GetById(int idf)
        {
            Famillemodal _response = new Famillemodal();
            var _data = await this.context.TblFamilles.FindAsync(idf);
            if (_data != null)
            {
                _response = this.mapper.Map<TblFamille, Famillemodal>(_data);
            }
            return _response;
        }

        public async Task<APIResponse> Remove(string code)
        {
            APIResponse response = new APIResponse();

            // Convert the string 'code' to an int before passing to FindAsync
            if (int.TryParse(code, out int parsedCode))
            {
                try
                {
                    var _famille = await this.context.TblFamilles.FindAsync(parsedCode);
                    if (_famille != null)
                    {
                        this.context.TblFamilles.Remove(_famille);
                        await this.context.SaveChangesAsync();
                        response.ResponseCode = 200;
                        response.Result = "pass";
                    }
                    else
                    {
                        response.ResponseCode = 404;
                        response.Message = "Data not found";
                    }
                }
                catch (Exception ex)
                {
                    response.ResponseCode = 400;
                    response.Message = ex.Message;
                    this.logger.LogError(ex.Message, ex);
                }
            }
            else
            {
                response.ResponseCode = 400;
                response.Message = "Invalid code format. Code must be an integer.";
            }

            return response;
        }

        public async Task<APIResponse> Update(Famillemodal data, string code)
        {
            APIResponse response = new APIResponse();

            // Convert the string 'code' to an int before passing to FindAsync
            if (int.TryParse(code, out int parsedCode))
            {
                try
                {
                    var _famille = await this.context.TblFamilles.FindAsync(parsedCode);
                    if (_famille != null)
                    {
                        _famille.nomf = data.nomf;

                        _famille.IsActive = data.IsActive;
                        await this.context.SaveChangesAsync();
                        response.ResponseCode = 200;
                        response.Result = "pass";
                    }
                    else
                    {
                        response.ResponseCode = 404;
                        response.Message = "Data not found";
                    }
                }
                catch (Exception ex)
                {
                    response.ResponseCode = 400;
                    response.Message = ex.Message;
                    this.logger.LogError(ex.Message, ex);
                }
            }
            else
            {
                response.ResponseCode = 400;
                response.Message = "Invalid code format. Code must be an integer.";
            }

            return response;
        }
    }
}
