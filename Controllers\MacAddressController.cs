﻿using LearnAPI.Service;
using Microsoft.AspNetCore.Mvc;

[ApiController]
[Route("api/[controller]")]
public class MacAddressController : ControllerBase
{
    private readonly IEncryptionService _encryptionService;

    public MacAddressController(IEncryptionService encryptionService)
    {
        _encryptionService = encryptionService;
    }

    [HttpPost("encrypt")]
    public IActionResult Encrypt([FromBody] string plainText)
    {
        var encryptedText = _encryptionService.Encrypt(plainText);
        return Ok(new { EncryptedText = encryptedText });
    }

    [HttpPost("decrypt")]
    public IActionResult Decrypt([FromBody] string cipherText)
    {
        var decryptedText = _encryptionService.Decrypt(cipherText);
        return Ok(new { DecryptedText = decryptedText });
    }
}