﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

public class CA_HT_Par_Moismodal
{
    public int Nombre_de_doc { get; set; } // Identifiant unique de la ligne de devis

    public int Année { get; set; } // Clé étrangère

    public int Mois { get; set; } // Référence à l'article


    [Column(TypeName = "decimal(20, 3)")]
    public decimal CA_HT { get; set; } // Montant TTC (calculé)

    //  [StringLength(50)]
    public string typeDoc { get; set; }

}