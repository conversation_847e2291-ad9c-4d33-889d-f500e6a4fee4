using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Data.SqlClient;
using System;

namespace Controllers
{
    public class PointageFilter
    {
        public int? UtilisateurId { get; set; }
        public DateTime? DateDebut { get; set; }
        public DateTime? DateFin { get; set; }
        public string? Type { get; set; }
        public string? Methode { get; set; }
        public string? Lieu { get; set; }
        public string? Statut { get; set; }
    }

    public class PointageModel
    {
        public int Id { get; set; }
        public int UtilisateurId { get; set; }
        public DateTime Horodatage { get; set; }
        public string? Type { get; set; }
        public string? Methode { get; set; }
        public string? Lieu { get; set; }
        public string? Statut { get; set; }
        public DateTime? HeureSortie { get; set; }
    }

    [ApiController]
    [Route("api/[controller]")]
    public class PointageController : ControllerBase
    {
        private readonly string _connectionString = "Server=DESKTOP-JM9HQ35\\MSSQLSERVER01;Database=Pointage_DB;Trusted_Connection=True;";

        [HttpGet]
        public IActionResult GetAll()
        {
            try
            {
                var pointages = new List<PointageModel>();
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("SELECT TOP (1000) [Id], [UtilisateurId], [Horodatage], [Type], [Methode], [Lieu], [Statut], [HeureSortie] FROM [Pointage_DB].[dbo].[Pointages]", connection);
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            pointages.Add(new PointageModel
                            {
                                Id = reader.GetInt32(0),
                                UtilisateurId = reader.GetInt32(1),
                                Horodatage = reader.GetDateTime(2),
                                Type = reader.IsDBNull(3) ? null : reader.GetString(3),
                                Methode = reader.IsDBNull(4) ? null : reader.GetString(4),
                                Lieu = reader.IsDBNull(5) ? null : reader.GetString(5),
                                Statut = reader.IsDBNull(6) ? null : reader.GetString(6),
                                HeureSortie = reader.IsDBNull(7) ? null : reader.GetDateTime(7)
                            });
                        }
                    }
                }
                return Ok(pointages);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans GetAll: {ex.Message}\nStackTrace: {ex.StackTrace}");
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpGet("listing-dynamique")]
        public IActionResult GetAllPointagesDynamic()
        {
            try
            {
                var pointages = new List<PointageModel>();
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("SELECT [Id], [UtilisateurId], [Horodatage], [Type], [Methode], [Lieu], [Statut], [HeureSortie] FROM [Pointage_DB].[dbo].[Pointages]", connection);
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            pointages.Add(new PointageModel
                            {
                                Id = reader.GetInt32(0),
                                UtilisateurId = reader.GetInt32(1),
                                Horodatage = reader.GetDateTime(2),
                                Type = reader.IsDBNull(3) ? null : reader.GetString(3),
                                Methode = reader.IsDBNull(4) ? null : reader.GetString(4),
                                Lieu = reader.IsDBNull(5) ? null : reader.GetString(5),
                                Statut = reader.IsDBNull(6) ? null : reader.GetString(6),
                                HeureSortie = reader.IsDBNull(7) ? null : reader.GetDateTime(7)
                            });
                        }
                    }
                }
                return Ok(pointages);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans GetAllPointagesDynamic: {ex.Message}\nStackTrace: {ex.StackTrace}");
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpPost]
        public IActionResult CreatePointage([FromBody] PointageModel pointage)
        {
            try
            {
                Console.WriteLine($"📥 Requête reçue pour CreatePointage: {System.Text.Json.JsonSerializer.Serialize(pointage)}");
                return AddPointageInternal(pointage);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans CreatePointage: {ex.Message}\nStackTrace: {ex.StackTrace}");
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpPost("add")]
        public IActionResult AddPointage([FromBody] PointageModel pointage)
        {
            try
            {
                Console.WriteLine($"📥 Requête reçue pour AddPointage: {System.Text.Json.JsonSerializer.Serialize(pointage)}");
                return AddPointageInternal(pointage);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans AddPointage: {ex.Message}\nStackTrace: {ex.StackTrace}");
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        private IActionResult AddPointageInternal(PointageModel pointage)
        {
            try
            {
                if (pointage == null || pointage.UtilisateurId <= 0 || pointage.Horodatage == default)
                {
                    Console.WriteLine($"❌ Validation échouée: UtilisateurId={pointage?.UtilisateurId}, Horodatage={pointage?.Horodatage}");
                    return BadRequest("Les données du pointage sont invalides ou incomplètes.");
                }

                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var (allowedMethodes, allowedStatuts) = GetAllowedValues(connection);

                    if (!string.IsNullOrEmpty(pointage.Methode) && !allowedMethodes.Contains(pointage.Methode))
                    {
                        Console.WriteLine($"❌ Methode invalide: {pointage.Methode}, Attendu: {string.Join(", ", allowedMethodes)}");
                        return BadRequest($"La valeur de Methode doit être l'une des suivantes : {string.Join(", ", allowedMethodes)} ou null.");
                    }
                    if (!string.IsNullOrEmpty(pointage.Statut) && !allowedStatuts.Contains(pointage.Statut))
                    {
                        Console.WriteLine($"❌ Statut invalide: {pointage.Statut}, Attendu: {string.Join(", ", allowedStatuts)}");
                        return BadRequest($"La valeur de Statut doit être l'une des suivantes : {string.Join(", ", allowedStatuts)} ou null.");
                    }

                    var sql = @"INSERT INTO [Pointage_DB].[dbo].[Pointages]
                                ([UtilisateurId], [Horodatage], [Type], [Methode], [Lieu], [Statut], [HeureSortie])
                                VALUES (@UtilisateurId, @Horodatage, @Type, @Methode, @Lieu, @Statut, @HeureSortie);
                                SELECT SCOPE_IDENTITY();";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@UtilisateurId", pointage.UtilisateurId);
                        command.Parameters.AddWithValue("@Horodatage", pointage.Horodatage);
                        command.Parameters.AddWithValue("@Type", (object?)pointage.Type ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Methode", (object?)pointage.Methode ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Lieu", (object?)pointage.Lieu ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Statut", (object?)pointage.Statut ?? DBNull.Value);
                        command.Parameters.AddWithValue("@HeureSortie", (object?)pointage.HeureSortie ?? DBNull.Value);

                        var newId = Convert.ToInt32(command.ExecuteScalar());
                        pointage.Id = newId;

                        Console.WriteLine($"✅ Pointage ajouté avec ID: {newId}");
                        return CreatedAtAction(nameof(GetAll), new { id = newId }, pointage);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans AddPointageInternal: {ex.Message}\nStackTrace: {ex.StackTrace}");
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpGet("{id}")]
        public IActionResult GetById(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("SELECT [Id], [UtilisateurId], [Horodatage], [Type], [Methode], [Lieu], [Statut], [HeureSortie] FROM [Pointage_DB].[dbo].[Pointages] WHERE Id = @Id", connection);
                    command.Parameters.AddWithValue("@Id", id);
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var pointage = new PointageModel
                            {
                                Id = reader.GetInt32(0),
                                UtilisateurId = reader.GetInt32(1),
                                Horodatage = reader.GetDateTime(2),
                                Type = reader.IsDBNull(3) ? null : reader.GetString(3),
                                Methode = reader.IsDBNull(4) ? null : reader.GetString(4),
                                Lieu = reader.IsDBNull(5) ? null : reader.GetString(5),
                                Statut = reader.IsDBNull(6) ? null : reader.GetString(6),
                                HeureSortie = reader.IsDBNull(7) ? null : reader.GetDateTime(7)
                            };
                            return Ok(pointage);
                        }
                        else
                        {
                            return NotFound($"Pointage avec ID {id} non trouvé.");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans GetById: {ex.Message}\nStackTrace: {ex.StackTrace}");
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpPut("{id}")]
        public IActionResult UpdatePointage(int id, [FromBody] PointageModel pointage)
        {
            try
            {
                if (pointage == null || pointage.UtilisateurId <= 0 || pointage.Horodatage == default)
                {
                    return BadRequest("Les données du pointage sont invalides ou incomplètes.");
                }

                // Vérifier si le pointage existe
                using (var connection = new SqlConnection(_connectionString))
                {
                    var checkCommand = new SqlCommand("SELECT COUNT(*) FROM [Pointage_DB].[dbo].[Pointages] WHERE Id = @Id", connection);
                    checkCommand.Parameters.AddWithValue("@Id", id);
                    connection.Open();
                    var exists = (int)checkCommand.ExecuteScalar() > 0;

                    if (!exists)
                    {
                        return NotFound($"Pointage avec ID {id} non trouvé.");
                    }

                    // Valider les contraintes
                    var (allowedMethodes, allowedStatuts) = GetAllowedValues(connection);

                    if (!string.IsNullOrEmpty(pointage.Methode) && !allowedMethodes.Contains(pointage.Methode))
                    {
                        return BadRequest($"La valeur de Methode doit être l'une des suivantes : {string.Join(", ", allowedMethodes)} ou null.");
                    }
                    if (!string.IsNullOrEmpty(pointage.Statut) && !allowedStatuts.Contains(pointage.Statut))
                    {
                        return BadRequest($"La valeur de Statut doit être l'une des suivantes : {string.Join(", ", allowedStatuts)} ou null.");
                    }

                    // Mettre à jour
                    var sql = @"UPDATE [Pointage_DB].[dbo].[Pointages]
                                SET [UtilisateurId] = @UtilisateurId,
                                    [Horodatage] = @Horodatage,
                                    [Type] = @Type,
                                    [Methode] = @Methode,
                                    [Lieu] = @Lieu,
                                    [Statut] = @Statut,
                                    [HeureSortie] = @HeureSortie
                                WHERE Id = @Id";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@Id", id);
                        command.Parameters.AddWithValue("@UtilisateurId", pointage.UtilisateurId);
                        command.Parameters.AddWithValue("@Horodatage", pointage.Horodatage);
                        command.Parameters.AddWithValue("@Type", (object?)pointage.Type ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Methode", (object?)pointage.Methode ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Lieu", (object?)pointage.Lieu ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Statut", (object?)pointage.Statut ?? DBNull.Value);
                        command.Parameters.AddWithValue("@HeureSortie", (object?)pointage.HeureSortie ?? DBNull.Value);

                        var rowsAffected = command.ExecuteNonQuery();
                        if (rowsAffected > 0)
                        {
                            pointage.Id = id;
                            Console.WriteLine($"✅ Pointage {id} mis à jour avec succès");
                            return Ok(pointage);
                        }
                        else
                        {
                            return StatusCode(500, "Erreur lors de la mise à jour du pointage.");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans UpdatePointage: {ex.Message}\nStackTrace: {ex.StackTrace}");
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpDelete("{id}")]
        public IActionResult DeletePointage(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    // Vérifier si le pointage existe
                    var checkCommand = new SqlCommand("SELECT COUNT(*) FROM [Pointage_DB].[dbo].[Pointages] WHERE Id = @Id", connection);
                    checkCommand.Parameters.AddWithValue("@Id", id);
                    connection.Open();
                    var exists = (int)checkCommand.ExecuteScalar() > 0;

                    if (!exists)
                    {
                        return NotFound($"Pointage avec ID {id} non trouvé.");
                    }

                    // Supprimer
                    var command = new SqlCommand("DELETE FROM [Pointage_DB].[dbo].[Pointages] WHERE Id = @Id", connection);
                    command.Parameters.AddWithValue("@Id", id);
                    var rowsAffected = command.ExecuteNonQuery();

                    if (rowsAffected > 0)
                    {
                        Console.WriteLine($"✅ Pointage {id} supprimé avec succès");
                        return Ok(new { message = $"Pointage {id} supprimé avec succès." });
                    }
                    else
                    {
                        return StatusCode(500, "Erreur lors de la suppression du pointage.");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans DeletePointage: {ex.Message}\nStackTrace: {ex.StackTrace}");
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpGet("constraints")]
        public IActionResult GetConstraints()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    // Récupérer toutes les contraintes CHECK de la table Pointages
                    var constraintsQuery = @"
                        SELECT
                            cc.name as constraint_name,
                            cc.definition,
                            c.name as column_name
                        FROM sys.check_constraints cc
                        INNER JOIN sys.columns c ON cc.parent_object_id = c.object_id
                        WHERE cc.parent_object_id = OBJECT_ID('dbo.Pointages')
                        ORDER BY c.name";

                    var constraints = new List<object>();
                    using (var cmd = new SqlCommand(constraintsQuery, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                constraints.Add(new
                                {
                                    constraintName = reader["constraint_name"].ToString(),
                                    definition = reader["definition"].ToString(),
                                    columnName = reader["column_name"].ToString()
                                });
                            }
                        }
                    }

                    var (allowedMethodes, allowedStatuts) = GetAllowedValues(connection);

                    // Récupérer les valeurs distinctes existantes dans la table
                    var existingValuesQuery = @"
                        SELECT DISTINCT
                            Type, Methode, Statut
                        FROM [Pointage_DB].[dbo].[Pointages]
                        WHERE Type IS NOT NULL OR Methode IS NOT NULL OR Statut IS NOT NULL";

                    var existingValues = new List<object>();
                    using (var cmd = new SqlCommand(existingValuesQuery, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                existingValues.Add(new
                                {
                                    type = reader["Type"] == DBNull.Value ? null : reader["Type"].ToString(),
                                    methode = reader["Methode"] == DBNull.Value ? null : reader["Methode"].ToString(),
                                    statut = reader["Statut"] == DBNull.Value ? null : reader["Statut"].ToString()
                                });
                            }
                        }
                    }

                    return Ok(new
                    {
                        allowedMethodes = allowedMethodes,
                        allowedStatuts = allowedStatuts,
                        allowedTypes = new List<string> { "Entrée", "Sortie", "Pause", "Reprise" },
                        constraints = constraints,
                        existingValues = existingValues,
                        recommendation = new
                        {
                            message = "Utilisez les valeurs existantes dans la base de données ou vérifiez les contraintes CHECK",
                            suggestedStatut = existingValues.Where(v => v.GetType().GetProperty("statut")?.GetValue(v) != null)
                                                           .Select(v => v.GetType().GetProperty("statut")?.GetValue(v))
                                                           .Distinct()
                                                           .ToList()
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans GetConstraints: {ex.Message}\nStackTrace: {ex.StackTrace}");
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpPost("dynamic")]
        public IActionResult GetPointagesDynamic([FromBody] PointageFilter filter)
        {
            try
            {
                var pointages = new List<PointageModel>();
                using (var connection = new SqlConnection(_connectionString))
                {
                    var sql = "SELECT [Id], [UtilisateurId], [Horodatage], [Type], [Methode], [Lieu], [Statut] FROM [Pointage_DB].[dbo].[Pointages] WHERE 1=1";
                    var cmd = new SqlCommand();
                    if (filter.UtilisateurId.HasValue)
                    {
                        sql += " AND UtilisateurId = @UtilisateurId";
                        cmd.Parameters.AddWithValue("@UtilisateurId", filter.UtilisateurId.Value);
                    }
                    if (filter.DateDebut.HasValue)
                    {
                        sql += " AND Horodatage >= @DateDebut";
                        cmd.Parameters.AddWithValue("@DateDebut", filter.DateDebut.Value);
                    }
                    if (filter.DateFin.HasValue)
                    {
                        sql += " AND Horodatage <= @DateFin";
                        cmd.Parameters.AddWithValue("@DateFin", filter.DateFin.Value);
                    }
                    if (!string.IsNullOrEmpty(filter.Type))
                    {
                        sql += " AND Type = @Type";
                        cmd.Parameters.AddWithValue("@Type", filter.Type);
                    }
                    if (!string.IsNullOrEmpty(filter.Methode))
                    {
                        sql += " AND Methode = @Methode";
                        cmd.Parameters.AddWithValue("@Methode", filter.Methode);
                    }
                    if (!string.IsNullOrEmpty(filter.Lieu))
                    {
                        sql += " AND Lieu = @Lieu";
                        cmd.Parameters.AddWithValue("@Lieu", filter.Lieu);
                    }
                    if (!string.IsNullOrEmpty(filter.Statut))
                    {
                        sql += " AND Statut = @Statut";
                        cmd.Parameters.AddWithValue("@Statut", filter.Statut);
                    }
                    cmd.CommandText = sql;
                    cmd.Connection = connection;
                    connection.Open();
                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            pointages.Add(new PointageModel
                            {
                                Id = reader.GetInt32(0),
                                UtilisateurId = reader.GetInt32(1),
                                Horodatage = reader.GetDateTime(2),
                                Type = reader.IsDBNull(3) ? null : reader.GetString(3),
                                Methode = reader.IsDBNull(4) ? null : reader.GetString(4),
                                Lieu = reader.IsDBNull(5) ? null : reader.GetString(5),
                                Statut = reader.IsDBNull(6) ? null : reader.GetString(6)
                            });
                        }
                    }
                }
                return Ok(pointages);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans GetPointagesDynamic: {ex.Message}\nStackTrace: {ex.StackTrace}");
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        private (List<string> allowedMethodes, List<string> allowedStatuts) GetAllowedValues(SqlConnection connection)
        {
            var allowedMethodes = new List<string>();
            var allowedStatuts = new List<string>();

            try
            {
                // Récupérer toutes les contraintes CHECK de la table
                string allConstraintsQuery = @"
                    SELECT
                        cc.name as constraint_name,
                        cc.definition,
                        c.name as column_name
                    FROM sys.check_constraints cc
                    INNER JOIN sys.columns c ON cc.parent_object_id = c.object_id
                        AND cc.parent_column_id = c.column_id
                    WHERE cc.parent_object_id = OBJECT_ID('dbo.Pointages')
                    ORDER BY c.name";

                using (var checkCmd = new SqlCommand(allConstraintsQuery, connection))
                {
                    using (var reader = checkCmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var constraintName = reader["constraint_name"].ToString();
                            var definition = reader["definition"].ToString();
                            var columnName = reader["column_name"].ToString();

                            Console.WriteLine($"🔍 Contrainte trouvée: {constraintName} sur colonne {columnName}");
                            Console.WriteLine($"    Définition: {definition}");

                            // Extraire les valeurs entre guillemets simples
                            var matches = System.Text.RegularExpressions.Regex.Matches(definition, @"'([^']+)'");
                            var values = new List<string>();
                            foreach (System.Text.RegularExpressions.Match m in matches)
                            {
                                if (m.Groups.Count > 1)
                                {
                                    values.Add(m.Groups[1].Value);
                                }
                            }

                            if (values.Count > 0)
                            {
                                Console.WriteLine($"    Valeurs extraites: {string.Join(", ", values)}");

                                if (columnName.Equals("Methode", StringComparison.OrdinalIgnoreCase))
                                {
                                    allowedMethodes.AddRange(values);
                                }
                                else if (columnName.Equals("Statut", StringComparison.OrdinalIgnoreCase))
                                {
                                    allowedStatuts.AddRange(values);
                                }
                            }
                        }
                    }
                }

                // Si aucune contrainte trouvée, récupérer les valeurs existantes dans la table
                if (allowedStatuts.Count == 0)
                {
                    Console.WriteLine("🔍 Aucune contrainte Statut trouvée, récupération des valeurs existantes...");
                    string existingStatutsQuery = @"
                        SELECT DISTINCT Statut
                        FROM [Pointage_DB].[dbo].[Pointages]
                        WHERE Statut IS NOT NULL";

                    using (var cmd = new SqlCommand(existingStatutsQuery, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var statut = reader["Statut"].ToString();
                                if (!string.IsNullOrEmpty(statut) && !allowedStatuts.Contains(statut))
                                {
                                    allowedStatuts.Add(statut);
                                }
                            }
                        }
                    }
                }

                if (allowedMethodes.Count == 0)
                {
                    Console.WriteLine("🔍 Aucune contrainte Methode trouvée, récupération des valeurs existantes...");
                    string existingMethodesQuery = @"
                        SELECT DISTINCT Methode
                        FROM [Pointage_DB].[dbo].[Pointages]
                        WHERE Methode IS NOT NULL";

                    using (var cmd = new SqlCommand(existingMethodesQuery, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var methode = reader["Methode"].ToString();
                                if (!string.IsNullOrEmpty(methode) && !allowedMethodes.Contains(methode))
                                {
                                    allowedMethodes.Add(methode);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors de la récupération des contraintes: {ex.Message}");
            }

            // Valeurs par défaut si rien trouvé
            if (allowedMethodes.Count == 0)
            {
                allowedMethodes = new List<string> { "web", "mobile", "biometric", "manual" };
            }
            if (allowedStatuts.Count == 0)
            {
                allowedStatuts = new List<string> { "normal", "retard", "absence", "justifie" };
            }

            Console.WriteLine($"✅ Méthodes autorisées finales: {string.Join(", ", allowedMethodes)}");
            Console.WriteLine($"✅ Statuts autorisés finaux: {string.Join(", ", allowedStatuts)}");

            return (allowedMethodes, allowedStatuts);
        }
    }
}