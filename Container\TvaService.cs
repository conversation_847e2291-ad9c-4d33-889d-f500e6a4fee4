﻿using AutoMapper;
using LearnAPI.Helper;
using LearnAPI.Modal;
using LearnAPI.Repos;
using LearnAPI.Repos.Models;
using LearnAPI.Service;
using Microsoft.EntityFrameworkCore;

namespace LearnAPI.Container
{
    public class TvaService : ITvaService
    {
        private readonly LearndataContext context;
        private readonly IMapper mapper;
        private readonly ILogger<TvaService> logger;

        public TvaService(LearndataContext context, IMapper mapper, ILogger<TvaService> logger)
        {
            this.context = context;
            this.mapper = mapper;
            this.logger = logger;
        }
        
        public async Task<APIResponse> Create(Tvamodal data)
        {
            APIResponse response = new APIResponse();
            try
            {
                this.logger.LogInformation("Create Begins");
                TblTva _tva = this.mapper.Map<Tvamodal, TblTva>(data);
                await this.context.TblTvas.AddAsync(_tva);
                await this.context.SaveChangesAsync();
                response.ResponseCode = 201;
                response.Result = "pass";
            }
            catch (Exception ex)
            {
                response.ResponseCode = 400;
                response.Message = ex.Message;
                this.logger.LogError(ex.Message, ex);
            }
            return response;
        }
        

        public async Task<List<Tvamodal>> Getall()
        {
            List<Tvamodal> _response = new List<Tvamodal>();
            var _data = await this.context.TblTvas.ToListAsync();
            if (_data != null)
            {
                _response = this.mapper.Map<List<TblTva>, List<Tvamodal>>(_data);
            }
            return _response;
        }

        public async Task<Tvamodal> Getbycode(string code)
        {
            Tvamodal _response = new Tvamodal();

            // Convert the string 'code' to an int before passing to FindAsync
            if (int.TryParse(code, out int parsedCode))
            {
                var _data = await this.context.TblTvas.FindAsync(parsedCode);
                if (_data != null)
                {
                    _response = this.mapper.Map<TblTva, Tvamodal>(_data);
                }
            }
            return _response;
        }

        public async Task<Tvamodal> GetById(int idt)
        {
            Tvamodal _response = new Tvamodal();
            var _data = await this.context.TblTvas.FindAsync(idt);
            if (_data != null)
            {
                _response = this.mapper.Map<TblTva, Tvamodal>(_data);
            }
            return _response;
        }

        public async Task<APIResponse> Remove(string code)
        {
            APIResponse response = new APIResponse();

            // Convert the string 'code' to an int before passing to FindAsync
            if (int.TryParse(code, out int parsedCode))
            {
                try
                {
                    var _tva = await this.context.TblTvas.FindAsync(parsedCode);
                    if (_tva != null)
                    {
                        this.context.TblTvas.Remove(_tva);
                        await this.context.SaveChangesAsync();
                        response.ResponseCode = 200;
                        response.Result = "pass";
                    }
                    else
                    {
                        response.ResponseCode = 404;
                        response.Message = "Data not found";
                    }
                }
                catch (Exception ex)
                {
                    response.ResponseCode = 400;
                    response.Message = ex.Message;
                    this.logger.LogError(ex.Message, ex);
                }
            }
            else
            {
                response.ResponseCode = 400;
                response.Message = "Invalid code format. Code must be an integer.";
            }

            return response;
        }

        public async Task<APIResponse> Update(Tvamodal data, string code)
        {
            APIResponse response = new APIResponse();

            // Convert the string 'code' to an int before passing to FindAsync
            if (int.TryParse(code, out int parsedCode))
            {
                try
                {
                    var _tva = await this.context.TblTvas.FindAsync(parsedCode);
                    if (_tva != null)
                    {
                        _tva.nomtva = data.nomtva;
                        _tva.tauxtva = data.tauxtva;
                        _tva.Name = data.Name;
                        _tva.IsActive = data.IsActive;
                        await this.context.SaveChangesAsync();
                        response.ResponseCode = 200;
                        response.Result = "pass";
                    }
                    else
                    {
                        response.ResponseCode = 404;
                        response.Message = "Data not found";
                    }
                }
                catch (Exception ex)
                {
                    response.ResponseCode = 400;
                    response.Message = ex.Message;
                    this.logger.LogError(ex.Message, ex);
                }
            }
            else
            {
                response.ResponseCode = 400;
                response.Message = "Invalid code format. Code must be an integer.";
            }

            return response;
        }
    }
}
