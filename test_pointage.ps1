# Test script pour l'API Pointage

Write-Host "=== Test des contraintes ===" -ForegroundColor Green
try {
    $response = Invoke-WebRequest -Uri "https://localhost:4500/api/Pointage/constraints" -Method GET
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $constraints = $response.Content | ConvertFrom-Json
    Write-Host "Méthodes autorisées: $($constraints.allowedMethodes -join ', ')" -ForegroundColor Yellow
    Write-Host "Statuts autorisés: $($constraints.allowedStatuts -join ', ')" -ForegroundColor Yellow
    Write-Host "Types autorisés: $($constraints.allowedTypes -join ', ')" -ForegroundColor Yellow
} catch {
    Write-Host "Erreur lors de la récupération des contraintes: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test POST avec valeurs correctes ===" -ForegroundColor Green
$body = @{
    utilisateurId = 11
    horodatage = "2025-07-30T08:00:00"
    type = "Entrée"
    methode = "web"
    lieu = "Bureau"
    statut = "normal"
} | ConvertTo-Json

Write-Host "Body JSON: $body" -ForegroundColor Cyan

try {
    $response = Invoke-WebRequest -Uri "https://localhost:4500/api/Pointage/add" -Method POST -ContentType "application/json" -Body $body
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Green
} catch {
    Write-Host "Erreur lors du POST: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $errorContent = $reader.ReadToEnd()
        Write-Host "Détails de l'erreur: $errorContent" -ForegroundColor Red
    }
}
