﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LearnAPI.Repos.Models;

[Table("tbl_tva")]
public partial class TblTva
{
    [Key]
    public int idt { get; set; } 

    [Required]
    [MaxLength(100)]
    public string nomtva { get; set; }

    [Required]
    [Column(TypeName = "decimal(3, 0)")]
    [Range(0, 100)]
    public decimal? tauxtva { get; set; }   // Taux de TVA en pourcentage, 0, 7, 13,19.

    //public decimal? tauxtva { get; set; }   // Taux de TVA en pourcentage, 0, 7, 13,19.
   // public int tauxtva { get; set; }   // Taux de TVA en pourcentage, 0, 7, 13,19.

    [StringLength(50)]
    [Unicode(false)]
    public string Code { get; set; } = null!;

    [StringLength(50)]
    [Unicode(false)]
    public string Name { get; set; } = null!;

    public bool? IsActive { get; set; }

}
