using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Models; 

namespace Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class RoleController : ControllerBase
    {
         private readonly string _connectionString = "Server=DESKTOP-JM9HQ35\\MSSQLSERVER01;Database=Pointage_DB;Trusted_Connection=True;";

        public RoleController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("apicon")
                ?? throw new ArgumentNullException("La chaîne de connexion est manquante");
        }

        [HttpGet("getAllRoles")]
        public async Task<IActionResult> GetAllRoles()
        {
            try
            {
                var roles = new List<RoleModel>();
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    var cmd = new SqlCommand("SELECT code, name, status FROM [Pointage_DB].[dbo].[tbl_role]", connection);
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            roles.Add(new RoleModel
                            {
                                Code = reader.IsDBNull(0) ? 0 : int.TryParse(reader.GetString(0), out int code) ? code : 0,
                                Name = reader.GetString(1),
                                Status = reader.IsDBNull(2) ? "Inactive" : (reader.GetBoolean(2) ? "Active" : "Inactive"),
                                DateExpiration = null // Column doesn't exist in database
                            });
                        }
                    }
                }
                return Ok(roles);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, $"Erreur SQL : {ex.Message}");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur : {ex.Message}");
            }
        }

        [HttpPost("addRole")]
        public async Task<IActionResult> AddRole([FromBody] RoleModel role)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    var query = "INSERT INTO [Pointage_DB].[dbo].[tbl_role] (code, name, status) VALUES (@code, @name, @status)";
                    var cmd = new SqlCommand(query, connection);
                    cmd.Parameters.AddWithValue("@code", role.Code.ToString());
                    cmd.Parameters.AddWithValue("@name", role.Name);
                    cmd.Parameters.AddWithValue("@status", role.Status == "Active");

                    await cmd.ExecuteNonQueryAsync();
                }

                return Ok("Rôle ajouté avec succès.");
            }
            catch (SqlException ex)
            {
                return StatusCode(500, $"Erreur SQL : {ex.Message}");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur : {ex.Message}");
            }
        }
    }
}
