using System;

namespace Modal
{
    public class EmployeeModel
    {
        public int idE { get; set; }
        public string? Nom { get; set; }
        public string? Prenom { get; set; }
        public string? Matricule { get; set; }
        public string? Email { get; set; }
        public string? MotDePasse { get; set; }
        public string? Poste { get; set; }
        public string? Statut { get; set; }
        public int RoleId { get; set; }
        public string? telephone { get; set; }
        public string? adresse { get; set; }
        public string? ville { get; set; }
        public string? projet { get; set; }
        public DateTime? date_naissance { get; set; }
        public int? age { get; set; }
        public int? type_contrat_id { get; set; }
        public int? periode_id { get; set; }
        public string? username { get; set; }
        public int? departement_id { get; set; }
    }
}
