# Documentation - Méthode Dynamic-Join pour Employee-Verif

## Vue d'ensemble

La méthode `dynamic-join` permet de travailler de manière dynamique avec les tables Employee et Verif en utilisant `ExpandoObject`. Cette approche garantit que l'ajout ou la suppression de colonnes dans les tables se reflète automatiquement dans l'API sans modification du code.

## Endpoints disponibles

### 1. GET /api/EmployeeVerif/dynamic-join
Récupère tous les employés avec leurs données de vérification de manière dynamique.

**Paramètres optionnels :**
- `employeeId` (int) : Filtre par ID d'employé spécifique

**Exemple de requête :**
```
GET /api/EmployeeVerif/dynamic-join
GET /api/EmployeeVerif/dynamic-join?employeeId=123
```

**Exemple de réponse :**
```json
{
  "Data": [
    {
      "idE": 1,
      "Nom": "<PERSON><PERSON>",
      "Prenom": "<PERSON>",
      "Matricule": "EMP001",
      "Email": "<EMAIL>",
      "Poste": "Développeur",
      "Statut": "Actif",
      "VerifId": 1,
      "VerifCode": "CODE123",
      "VerifPassword": "hashedpassword",
      "VerifRfid": "RFID123",
      "NomComplet": "Jean Dupont",
      "HasVerificationData": true,
      "VerificationStatus": "Configuré",
      "SecurityLevel": "Élevée",
      "AuthenticationMethods": ["Mot de passe", "RFID", "Reconnaissance faciale"]
    }
  ],
  "Metadata": {
    "EmployeeTable": {
      "TableName": "employee",
      "Columns": [...]
    },
    "VerifTable": {
      "TableName": "verif", 
      "Columns": [...]
    },
    "CalculatedProperties": [
      "NomComplet",
      "HasVerificationData", 
      "VerificationStatus",
      "AgeCalcule",
      "SecurityLevel",
      "AuthenticationMethods"
    ]
  },
  "Count": 1,
  "Timestamp": "2024-01-01T12:00:00Z"
}
```

### 2. GET /api/EmployeeVerif/dynamic-columns/{tableName}
Récupère la structure des colonnes d'une table spécifique.

**Paramètres :**
- `tableName` (string) : Nom de la table (employee, verif, pointage)

**Exemple de requête :**
```
GET /api/EmployeeVerif/dynamic-columns/employee
```

**Exemple de réponse :**
```json
{
  "TableName": "employee",
  "Columns": [
    {
      "ColumnName": "idE",
      "DataType": "int",
      "IsNullable": false,
      "MaxLength": null,
      "NumericPrecision": "10",
      "NumericScale": "0",
      "DefaultValue": null,
      "Position": 1
    },
    {
      "ColumnName": "Nom",
      "DataType": "nvarchar",
      "IsNullable": true,
      "MaxLength": "100",
      "NumericPrecision": null,
      "NumericScale": null,
      "DefaultValue": null,
      "Position": 2
    }
  ],
  "ColumnCount": 15,
  "Timestamp": "2024-01-01T12:00:00Z"
}
```

### 3. PUT /api/EmployeeVerif/dynamic-update/{employeeId}
Met à jour dynamiquement un employé et ses données de vérification.

**Paramètres :**
- `employeeId` (int) : ID de l'employé à mettre à jour

**Corps de la requête (ExpandoObject) :**
```json
{
  "Nom": "Nouveau Nom",
  "Email": "<EMAIL>",
  "VerifPassword": "nouveaumotdepasse",
  "VerifRfid": "NOUVEAURFID123"
}
```

**Exemple de réponse :**
```json
{
  "message": "Mise à jour réussie",
  "employeeId": 123,
  "updatedEmployeeFields": ["Nom", "Email"],
  "updatedVerifFields": ["Password", "Rfid"],
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Propriétés calculées automatiques

Le système ajoute automatiquement des propriétés calculées :

1. **NomComplet** : Concaténation du prénom et du nom
2. **HasVerificationData** : Booléen indiquant si l'employé a des données de vérification
3. **VerificationStatus** : "Configuré" ou "Non configuré"
4. **AgeCalcule** : Âge calculé à partir de la date de naissance
5. **SecurityLevel** : Niveau de sécurité basé sur les méthodes d'authentification
6. **AuthenticationMethods** : Liste des méthodes d'authentification disponibles

## Avantages de l'approche dynamique

1. **Flexibilité** : Ajout/suppression de colonnes sans modification du code
2. **Maintenabilité** : Moins de code à maintenir
3. **Évolutivité** : S'adapte automatiquement aux changements de schéma
4. **Performance** : Récupération optimisée des données
5. **Sécurité** : Validation des noms de tables autorisées

## Gestion des erreurs

Toutes les méthodes incluent une gestion d'erreurs complète avec :
- Messages d'erreur détaillés
- Codes de statut HTTP appropriés
- Informations de débogage en cas d'exception

## Sécurité

- Validation des noms de tables autorisées
- Utilisation de paramètres SQL pour éviter l'injection SQL
- Gestion des transactions pour les mises à jour
- Validation des données d'entrée

## Exemples d'utilisation

### Récupérer tous les employés avec vérification
```javascript
fetch('/api/EmployeeVerif/dynamic-join')
  .then(response => response.json())
  .then(data => {
    console.log('Employés:', data.Data);
    console.log('Métadonnées:', data.Metadata);
  });
```

### Mettre à jour un employé dynamiquement
```javascript
const updateData = {
  Nom: "Nouveau Nom",
  Email: "<EMAIL>",
  VerifPassword: "nouveaumotdepasse"
};

fetch('/api/EmployeeVerif/dynamic-update/123', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(updateData)
})
.then(response => response.json())
.then(data => console.log('Mise à jour:', data));
```

### Récupérer la structure d'une table
```javascript
fetch('/api/EmployeeVerif/dynamic-columns/employee')
  .then(response => response.json())
  .then(data => {
    console.log('Structure de la table:', data.Columns);
  });
```

## Nouvelles méthodes ajoutées

### 4. POST /api/EmployeeVerif/dynamic-create
Crée dynamiquement un nouvel employé avec ses données de vérification.

**Corps de la requête (ExpandoObject) :**
```json
{
  "Nom": "Nouveau Employé",
  "Prenom": "Prénom",
  "Email": "<EMAIL>",
  "Matricule": "EMP999",
  "Poste": "Développeur",
  "VerifCode": "CODE999",
  "VerifPassword": "motdepasse123"
}
```

**Exemple de réponse :**
```json
{
  "message": "Enregistrement Employee-Verif créé avec succès",
  "employeeId": 999,
  "data": {
    "idE": 999,
    "Nom": "Nouveau Employé",
    "Prenom": "Prénom",
    "Email": "<EMAIL>",
    "Matricule": "EMP999",
    "Poste": "Développeur",
    "VerifCode": "CODE999",
    "NomComplet": "Prénom Nouveau Employé",
    "HasVerificationData": true,
    "VerificationStatus": "Configuré"
  },
  "fieldsProcessed": {
    "employeeFields": ["Nom", "Prenom", "Email", "Matricule", "Poste"],
    "verifFields": ["Code", "Password"]
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 5. GET /api/EmployeeVerif/dynamic-getall
Récupère tous les employés avec pagination, tri et filtrage dynamiques.

**Paramètres optionnels :**
- `page` (int) : Numéro de page (défaut: 1)
- `pageSize` (int) : Taille de page (défaut: 50, max: 1000)
- `sortBy` (string) : Colonne de tri (défaut: "idE")
- `sortOrder` (string) : Ordre de tri ASC/DESC (défaut: "ASC")
- `filter` (string) : Filtre de recherche sur Nom, Prénom, Email, Matricule

**Exemple de requête :**
```
GET /api/EmployeeVerif/dynamic-getall
GET /api/EmployeeVerif/dynamic-getall?page=2&pageSize=20&sortBy=Nom&sortOrder=DESC
GET /api/EmployeeVerif/dynamic-getall?filter=Dupont&page=1&pageSize=10
```

**Exemple de réponse :**
```json
{
  "Data": [
    {
      "idE": 1,
      "Nom": "Dupont",
      "Prenom": "Jean",
      "Matricule": "EMP001",
      "Email": "<EMAIL>",
      "NomComplet": "Jean Dupont",
      "HasVerificationData": true,
      "VerificationStatus": "Configuré"
    }
  ],
  "Pagination": {
    "CurrentPage": 1,
    "PageSize": 50,
    "TotalCount": 150,
    "TotalPages": 3,
    "HasNextPage": true,
    "HasPreviousPage": false
  },
  "Filters": {
    "SortBy": "idE",
    "SortOrder": "ASC",
    "Filter": null
  },
  "Metadata": {
    "EmployeeTable": {
      "TableName": "employee",
      "Columns": [...]
    },
    "VerifTable": {
      "TableName": "verif",
      "Columns": [...]
    }
  },
  "Timestamp": "2024-01-01T12:00:00Z"
}
```

## Exemples d'utilisation des nouvelles méthodes

### Créer un nouvel employé dynamiquement
```javascript
const newEmployee = {
  Nom: "Martin",
  Prenom: "Sophie",
  Email: "<EMAIL>",
  Matricule: "EMP888",
  Poste: "Analyste",
  VerifCode: "SOPHIE123",
  VerifPassword: "motdepasse456"
};

fetch('/api/EmployeeVerif/dynamic-create', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(newEmployee)
})
.then(response => response.json())
.then(data => {
  console.log('Employé créé:', data);
  console.log('Nouvel ID:', data.employeeId);
});
```

### Récupérer tous les employés avec pagination
```javascript
const params = new URLSearchParams({
  page: 1,
  pageSize: 20,
  sortBy: 'Nom',
  sortOrder: 'ASC',
  filter: 'Dupont'
});

fetch(`/api/EmployeeVerif/dynamic-getall?${params}`)
  .then(response => response.json())
  .then(data => {
    console.log('Employés:', data.Data);
    console.log('Pagination:', data.Pagination);
    console.log('Total:', data.Pagination.TotalCount);
  });
```

## Fonctionnalités complètes disponibles

1. **GET dynamic-join** - Récupération ciblée avec ou sans ID
2. **GET dynamic-getall** - Récupération complète avec pagination et filtrage
3. **POST dynamic-create** - Création dynamique d'employés
4. **PUT dynamic-update** - Mise à jour dynamique
5. **GET dynamic-columns** - Métadonnées des tables

Toutes ces méthodes s'adaptent automatiquement aux changements de structure des tables Employee et Verif, garantissant une flexibilité maximale pour votre application.
