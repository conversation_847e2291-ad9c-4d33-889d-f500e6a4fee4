# Documentation - Méthode Dynamic-Join pour Employee-Verif

## Vue d'ensemble

La méthode `dynamic-join` permet de travailler de manière dynamique avec les tables Employee et Verif en utilisant `ExpandoObject`. Cette approche garantit que l'ajout ou la suppression de colonnes dans les tables se reflète automatiquement dans l'API sans modification du code.

## Endpoints disponibles

### 1. GET /api/EmployeeVerif/dynamic-join
Récupère tous les employés avec leurs données de vérification de manière dynamique.

**Paramètres optionnels :**
- `employeeId` (int) : Filtre par ID d'employé spécifique

**Exemple de requête :**
```
GET /api/EmployeeVerif/dynamic-join
GET /api/EmployeeVerif/dynamic-join?employeeId=123
```

**Exemple de réponse :**
```json
{
  "Data": [
    {
      "idE": 1,
      "Nom": "<PERSON><PERSON>",
      "Prenom": "<PERSON>",
      "Matricule": "EMP001",
      "Email": "<EMAIL>",
      "Poste": "Développeur",
      "Statut": "Actif",
      "VerifId": 1,
      "VerifCode": "CODE123",
      "VerifPassword": "hashedpassword",
      "VerifRfid": "RFID123",
      "NomComplet": "Jean Dupont",
      "HasVerificationData": true,
      "VerificationStatus": "Configuré",
      "SecurityLevel": "Élevée",
      "AuthenticationMethods": ["Mot de passe", "RFID", "Reconnaissance faciale"]
    }
  ],
  "Metadata": {
    "EmployeeTable": {
      "TableName": "employee",
      "Columns": [...]
    },
    "VerifTable": {
      "TableName": "verif", 
      "Columns": [...]
    },
    "CalculatedProperties": [
      "NomComplet",
      "HasVerificationData", 
      "VerificationStatus",
      "AgeCalcule",
      "SecurityLevel",
      "AuthenticationMethods"
    ]
  },
  "Count": 1,
  "Timestamp": "2024-01-01T12:00:00Z"
}
```

### 2. GET /api/EmployeeVerif/dynamic-columns/{tableName}
Récupère la structure des colonnes d'une table spécifique.

**Paramètres :**
- `tableName` (string) : Nom de la table (employee, verif, pointage)

**Exemple de requête :**
```
GET /api/EmployeeVerif/dynamic-columns/employee
```

**Exemple de réponse :**
```json
{
  "TableName": "employee",
  "Columns": [
    {
      "ColumnName": "idE",
      "DataType": "int",
      "IsNullable": false,
      "MaxLength": null,
      "NumericPrecision": "10",
      "NumericScale": "0",
      "DefaultValue": null,
      "Position": 1
    },
    {
      "ColumnName": "Nom",
      "DataType": "nvarchar",
      "IsNullable": true,
      "MaxLength": "100",
      "NumericPrecision": null,
      "NumericScale": null,
      "DefaultValue": null,
      "Position": 2
    }
  ],
  "ColumnCount": 15,
  "Timestamp": "2024-01-01T12:00:00Z"
}
```

### 3. PUT /api/EmployeeVerif/dynamic-update/{employeeId}
Met à jour dynamiquement un employé et ses données de vérification.

**Paramètres :**
- `employeeId` (int) : ID de l'employé à mettre à jour

**Corps de la requête (ExpandoObject) :**
```json
{
  "Nom": "Nouveau Nom",
  "Email": "<EMAIL>",
  "VerifPassword": "nouveaumotdepasse",
  "VerifRfid": "NOUVEAURFID123"
}
```

**Exemple de réponse :**
```json
{
  "message": "Mise à jour réussie",
  "employeeId": 123,
  "updatedEmployeeFields": ["Nom", "Email"],
  "updatedVerifFields": ["Password", "Rfid"],
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Propriétés calculées automatiques

Le système ajoute automatiquement des propriétés calculées :

1. **NomComplet** : Concaténation du prénom et du nom
2. **HasVerificationData** : Booléen indiquant si l'employé a des données de vérification
3. **VerificationStatus** : "Configuré" ou "Non configuré"
4. **AgeCalcule** : Âge calculé à partir de la date de naissance
5. **SecurityLevel** : Niveau de sécurité basé sur les méthodes d'authentification
6. **AuthenticationMethods** : Liste des méthodes d'authentification disponibles

## Avantages de l'approche dynamique

1. **Flexibilité** : Ajout/suppression de colonnes sans modification du code
2. **Maintenabilité** : Moins de code à maintenir
3. **Évolutivité** : S'adapte automatiquement aux changements de schéma
4. **Performance** : Récupération optimisée des données
5. **Sécurité** : Validation des noms de tables autorisées

## Gestion des erreurs

Toutes les méthodes incluent une gestion d'erreurs complète avec :
- Messages d'erreur détaillés
- Codes de statut HTTP appropriés
- Informations de débogage en cas d'exception

## Sécurité

- Validation des noms de tables autorisées
- Utilisation de paramètres SQL pour éviter l'injection SQL
- Gestion des transactions pour les mises à jour
- Validation des données d'entrée

## Exemples d'utilisation

### Récupérer tous les employés avec vérification
```javascript
fetch('/api/EmployeeVerif/dynamic-join')
  .then(response => response.json())
  .then(data => {
    console.log('Employés:', data.Data);
    console.log('Métadonnées:', data.Metadata);
  });
```

### Mettre à jour un employé dynamiquement
```javascript
const updateData = {
  Nom: "Nouveau Nom",
  Email: "<EMAIL>",
  VerifPassword: "nouveaumotdepasse"
};

fetch('/api/EmployeeVerif/dynamic-update/123', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(updateData)
})
.then(response => response.json())
.then(data => console.log('Mise à jour:', data));
```

### Récupérer la structure d'une table
```javascript
fetch('/api/EmployeeVerif/dynamic-columns/employee')
  .then(response => response.json())
  .then(data => {
    console.log('Structure de la table:', data.Columns);
  });
```
