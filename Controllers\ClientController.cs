﻿using Microsoft.AspNetCore.Mvc;


using Microsoft.AspNetCore.Authorization;
using ClosedXML.Excel;
using LearnAPI.Modal;
using LearnAPI.Service;
using Microsoft.AspNetCore.RateLimiting;
using System.Data;

namespace LearnAPI.Controllers
{
    //[AllowAnonymous]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

   
        public class ClientController : ControllerBase
        {
            private readonly IClientService service;
            private readonly IWebHostEnvironment environment;
            public ClientController(IClientService service, IWebHostEnvironment environment)
            {
                this.service = service;
                this.environment = environment;
            }

        //[AllowAnonymous]
        // [EnableCors("corspolicy1")]
        [AllowAnonymous]
        [HttpGet("GetAll")]
            public async Task<IActionResult> GetAll()
            {
                var data = await this.service.Getall();
                if (data == null)
                {
                    return NotFound();
                }
                return Ok(data);
            }

        
        [DisableRateLimiting]

            [HttpGet("Getbycode")]
            public async Task<IActionResult> Getbycode(string code)
            {
                var data = await this.service.Getbycode(code);
                if (data == null)
                {
                    return NotFound();
                }
                return Ok(data);
            }

            [HttpPost("Create")]
            public async Task<IActionResult> Create(Clientmodal _data)
            {
                var data = await this.service.Create(_data);
                return Ok(data);
            }
            [HttpPut("Update")]
            public async Task<IActionResult> Update(Clientmodal _data, string code)
            {
                var data = await this.service.Update(_data, code);
                return Ok(data);
            }

            [HttpDelete("Remove")]
            public async Task<IActionResult> Remove(string code)
            {
                var data = await this.service.Remove(code);
                return Ok(data);
            }

            //[AllowAnonymous]
            [HttpGet("Exportexcel")]
            public async Task<IActionResult> Exportexcel()
            {
                try
                {
                    string Filepath = GetFilepath();
                    string excelpath = Filepath + "\\clientinfo.xlsx";
                    DataTable dt = new DataTable();
                    dt.Columns.Add("Code", typeof(string));
                    dt.Columns.Add("rs", typeof(string));
                    dt.Columns.Add("adr1", typeof(string));
                    dt.Columns.Add("adr2", typeof(string));
                    dt.Columns.Add("tel1", typeof(string));
                    dt.Columns.Add("tel2", typeof(string));
                    dt.Columns.Add("Email", typeof(string));
                    dt.Columns.Add("mf", typeof(string));
                    dt.Columns.Add("type", typeof(string));

                    dt.Columns.Add("exo", typeof(bool));
                    dt.Columns.Add("fodec", typeof(bool));
              
                var data = await this.service.Getall();
                    if (data != null && data.Count > 0)
                    {
                        data.ForEach(item =>
                        {
                            dt.Rows.Add(item.Code, item.rs, item.adr1, item.adr2, item.tel1, item.tel2, item.Email, item.mf, item.type, item.exo, item.fodec);
                        });
                    }
                    using (XLWorkbook wb = new XLWorkbook())
                    {
                        wb.AddWorksheet(dt, "Client Info");
                        using (MemoryStream stream = new MemoryStream())
                        {
                            wb.SaveAs(stream);

                            if (System.IO.File.Exists(excelpath))
                            {
                                System.IO.File.Delete(excelpath);
                            }
                            wb.SaveAs(excelpath);

                            return File(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Client.xlsx");
                        }
                    }
                }
                catch (Exception ex)
                {
                    return NotFound();
                }
            }

            [NonAction]
            private string GetFilepath()
            {
                return this.environment.WebRootPath + "\\Export";
            }
        }
    }
