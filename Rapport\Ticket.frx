﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="11/24/2024 00:37:33" ReportInfo.Modified="02/19/2025 20:00:40" ReportInfo.CreatorVersion="2024.1.7.0">
  <Dictionary>
    <MsSqlDataConnection Name="Connection1" ConnectionString="rijcmlq+rW0RkboK+/8CKGGYUID3ctBV8q9SvKYxl3iiE7vBnh7Tgou0+4WFf3NfmPSvHVFL5YF9bbPRhxoxGczvS+hHv0s0YU+BOBXXRWzzEAKN8sRtI6BHi3eKX1TjfRZ2B2QEhw8kyPAJKGzGhYk/csQgHWAWxcmwSX29SxFE5DKXNU=">
      <TableDataSource Name="Config_Société" DataType="System.Int32" Enabled="true" TableName="Config_Société">
        <Column Name="id" DataType="System.Int32"/>
        <Column Name="rs" DataType="System.String"/>
        <Column Name="adr1" DataType="System.String"/>
        <Column Name="adr2" DataType="System.String"/>
        <Column Name="tel1" DataType="System.String"/>
        <Column Name="tel2" DataType="System.String"/>
        <Column Name="Email" DataType="System.String"/>
        <Column Name="exo" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="mf" DataType="System.String"/>
      </TableDataSource>
      <TableDataSource Name="DocLines" DataType="System.Int32" Enabled="true" TableName="DocLines" SelectCommand="SELECT * FROM DocLines WHERE docid = @id&#10;">
        <Column Name="Code" DataType="System.String"/>
        <Column Name="codeArticle" DataType="System.String"/>
        <Column Name="desArticle" DataType="System.String"/>
        <Column Name="DocId" DataType="System.Int32"/>
        <Column Name="Id" DataType="System.Int32"/>
        <Column Name="idArticle" DataType="System.Int32"/>
        <Column Name="mHT" DataType="System.Decimal"/>
        <Column Name="mTTC" DataType="System.Decimal"/>
        <Column Name="prixUHT" DataType="System.Decimal"/>
        <Column Name="qte" DataType="System.Decimal"/>
        <Column Name="remise" DataType="System.Decimal"/>
        <Column Name="tauxTva" DataType="System.Decimal"/>
        <Column Name="touchstock" DataType="System.Int32"/>
        <Column Name="typeDoc" DataType="System.String"/>
        <CommandParameter Name="id" DataType="8" Expression="[id]"/>
      </TableDataSource>
      <TableDataSource Name="tbl_client" DataType="System.Int32" Enabled="true" TableName="tbl_client" SelectCommand="SELECT * FROM tbl_client WHERE idt = (SELECT idclient FROM docs WHERE id = @id)&#10;">
        <Column Name="idt" DataType="System.Int32"/>
        <Column Name="Code" DataType="System.String"/>
        <Column Name="rs" DataType="System.String"/>
        <Column Name="adr1" DataType="System.String"/>
        <Column Name="adr2" DataType="System.String"/>
        <Column Name="tel1" DataType="System.String"/>
        <Column Name="tel2" DataType="System.String"/>
        <Column Name="Email" DataType="System.String"/>
        <Column Name="exo" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="fodec" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="IsActive" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="Type" DataType="System.String"/>
        <Column Name="mf" DataType="System.String"/>
        <CommandParameter Name="id" DataType="8" Expression="[id]"/>
      </TableDataSource>
      <TableDataSource Name="Docs" DataType="System.Int32" Enabled="true" TableName="Docs" SelectCommand="SELECT * FROM DOCS WHERE ID = @Id">
        <Column Name="Id" DataType="System.Int32"/>
        <Column Name="Code" DataType="System.String"/>
        <Column Name="ndevis" DataType="System.Decimal"/>
        <Column Name="date" DataType="System.DateTime"/>
        <Column Name="idClient" DataType="System.Int32"/>
        <Column Name="codeClient" DataType="System.String"/>
        <Column Name="total" DataType="System.Decimal"/>
        <Column Name="typeReglement" DataType="System.String"/>
        <Column Name="typeDoc" DataType="System.String"/>
        <Column Name="IsActive" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="TotalTTC" DataType="System.Decimal"/>
        <Column Name="TotalHT" DataType="System.Decimal"/>
        <Column Name="TotalTVA" DataType="System.Decimal"/>
        <Column Name="Timbre" DataType="System.Decimal"/>
        <Column Name="MntLettre" DataType="System.String"/>
        <Column Name="iddocsrc" DataType="System.Int32"/>
        <Column Name="src" DataType="System.String"/>
        <CommandParameter Name="Id" DataType="8" Expression="[id]"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Relation Name="Docs_DocLines" ParentDataSource="null" ChildDataSource="DocLines" ParentColumns="Id" ChildColumns="DocId" Enabled="true"/>
    <Relation Name="Docs_tbl_client" ParentDataSource="null" ChildDataSource="tbl_client" ParentColumns="idClient" ChildColumns="idt" Enabled="true"/>
    <Relation Name="Docs_Proc_DocLines" ParentDataSource="null" ChildDataSource="DocLines" ParentColumns="Id" ChildColumns="DocId" Enabled="true"/>
    <Relation Name="Docs_DocLines1" ParentDataSource="null" ChildDataSource="DocLines" ParentColumns="Id" ChildColumns="DocId" Enabled="true"/>
    <Relation Name="Table_DocLines" ParentDataSource="null" ChildDataSource="DocLines" ParentColumns="Id" ChildColumns="DocId" Enabled="true"/>
    <Parameter Name="id" DataType="System.Int32" AsString=""/>
  </Dictionary>
  <ReportPage Name="Page1" PaperWidth="50" PaperHeight="60" LeftMargin="2.5" TopMargin="5" RightMargin="2.5" BottomMargin="5" Guides="236.25,406.35,453.6,699.3,472.5,689.85,37.8,170.1,302.4,409.7,255.15,132.3,217.35,18.9,415.8,519.75,623.7,557.55,585.9,245.7,396.9,756,415.8,415.8,406.35,415.8,415.8,415.8,415.8,396.9,406.35,406.35,406.35,368.55,368.55,103.95,103.95,103.95,103.95,189,141.75,132.3" Watermark.Font="Arial, 60pt" LastPageSource="1" FirstPageSource="1">
    <ReportTitleBand Name="ReportTitle1" Width="170.1" Height="211.25" Guides="56.7,85.05,94.5,207.9,103.95,122.85,132.3,151.2,160.65,179.55,189,207.9,9.45,141.75,211.25,198.45,207.9,66.15,207.9,217.35,217.35,217.35,207.9,207.9,207.9,207.9,207.9,207.9,207.9,207.9">
      <PictureObject Name="Picture2" Left="-141.75" Width="841.05" Height="75.6" CanGrow="true" GrowToBottom="true" Image=""/>
      <TextObject Name="Text3" Width="170.1" Height="28.35" CanGrow="true" GrowToBottom="true" CanBreak="false" Text="[Docs.typeDoc] N° [Docs.Code]" HorzAlign="Center" Font="Arial, 8pt, style=Bold"/>
      <ShapeObject Name="Shape1" Left="453.6" Top="94.5" Width="245.7" Height="116.75" GrowToBottom="true"/>
      <TextObject Name="Text4" Left="472.5" Top="103.95" Width="217.35" Height="18.9" CanGrow="true" GrowToBottom="true" Text="Client : [tbl_client.rs]" Font="Arial, 10pt"/>
      <TextObject Name="Text5" Left="472.5" Top="132.3" Width="217.35" Height="18.9" Text="MF : [tbl_client.mf]" Font="Arial, 10pt"/>
      <TextObject Name="Text6" Left="472.5" Top="160.65" Width="217.35" Height="18.9" Text="Tel : [tbl_client.tel1]" Font="Arial, 10pt"/>
      <TextObject Name="Text7" Left="472.5" Top="189" Width="217.35" Height="18.9" CanGrow="true" Text="Adresse : [tbl_client.adr1]" Font="Arial, 5pt"/>
      <TextObject Name="Text29" Top="37.8" Width="170.1" Height="18.9" CanGrow="true" GrowToBottom="true" CanBreak="false" Text="[Config_Société.rs]" Font="Arial, 8pt, style=Bold"/>
      <TextObject Name="Text30" Top="75.6" Width="170.1" Height="37.8" CanGrow="true" GrowToBottom="true" CanBreak="false" Text="Adresse : [Config_Société.adr1]" Font="Arial, 6pt"/>
      <TextObject Name="Text33" Top="56.7" Width="170.1" Height="18.9" CanGrow="true" GrowToBottom="true" CanBreak="false" Text="M.F. : [Config_Société.mf]" Font="Arial, 6pt"/>
      <TextObject Name="Text36" Top="28.35" Width="170.1" Height="9.45" CanGrow="true" GrowToBottom="true" CanBreak="false" Text="[Docs.date]" Format="Date" Format.Format="d" HorzAlign="Center" Font="Arial, 6pt"/>
      <TextObject Name="Text41" Top="116.75" Width="170.1" Height="18.9" Border.Lines="Top" Border.Style="DashDotDot" CanGrow="true" GrowToBottom="true" CanBreak="false" Text="Client : [tbl_client.rs]" HorzAlign="Center" VertAlign="Center" Font="Arial, 6pt"/>
      <TextObject Name="Text42" Top="135.65" Width="170.1" Height="18.9" CanGrow="true" GrowToBottom="true" CanBreak="false" Text="MF : [tbl_client.mf]" HorzAlign="Center" VertAlign="Center" Font="Arial, 6pt"/>
      <TextObject Name="Text44" Top="154.55" Width="170.1" Height="47.25" Border.Lines="Bottom" Border.Style="DashDot" CanGrow="true" GrowToBottom="true" CanBreak="false" Text="Adresse : [tbl_client.adr1]" HorzAlign="Center" VertAlign="Center" Font="Arial, 6pt"/>
    </ReportTitleBand>
    <PageHeaderBand Name="PageHeader1" Top="213.25" Width="170.1" Height="9.45" Border.Lines="Bottom" Border.Style="Dash" Guides="9.45,28.35001,37.8,37.8,37.8,28.35,47.25,37.8,28.35,28.35">
      <TextObject Name="Text8" Width="37.8" Height="9.45" Border.Lines="Bottom" Border.Style="Dash" Border.Width="0.25" Text="Code" HorzAlign="Center" VertAlign="Center" Font="Arial, 5pt, style=Bold"/>
      <TextObject Name="Text10" Left="85.05" Width="18.9" Height="9.45" Border.Lines="Bottom" Border.Style="Dash" Border.Width="0.25" Text="Qte" HorzAlign="Center" VertAlign="Center" Font="Arial, 5pt, style=Bold"/>
      <TextObject Name="Text13" Left="132.3" Width="37.8" Height="9.45" Border.Lines="Bottom" Border.Style="Dash" Border.Width="0.25" Text="TotalTTC" HorzAlign="Center" VertAlign="Center" Font="Arial, 5pt, style=Bold"/>
      <TextObject Name="Text9" Left="37.8" Width="47.25" Height="9.45" Border.Lines="Bottom" Border.Style="Dash" Border.Width="0.25" Text="Designation" HorzAlign="Center" VertAlign="Center" Font="Arial, 5pt, style=Bold"/>
      <TextObject Name="Text39" Left="103.95" Width="28.35" Height="9.45" Border.Lines="Bottom" Border.Style="Dash" Border.Width="0.25" Text="TVA" HorzAlign="Center" VertAlign="Center" Font="Arial, 5pt, style=Bold"/>
    </PageHeaderBand>
    <DataBand Name="Data1" Top="224.7" Width="170.1" Height="18.9" CanGrow="true" Guides="0,18.9,9.450012,18.9,9.45,9.45,9.45" DataSource="DocLines">
      <TextObject Name="Text14" Left="-18.9" Width="56.7" Height="9.45" Border.Lines="Bottom" Border.Style="Dash" Border.Width="0.25" CanGrow="true" GrowToBottom="true" CanBreak="false" Text="[DocLines.codeArticle]" HorzAlign="Center" VertAlign="Center" Font="Arial, 5pt"/>
      <TextObject Name="Text15" Left="37.8" Width="47.25" Height="9.45" Border.Lines="Bottom" Border.Style="Dash" Border.Width="0.25" CanGrow="true" GrowToBottom="true" CanBreak="false" Text="[DocLines.desArticle]" HorzAlign="Center" VertAlign="Center" Font="Arial, 5pt"/>
      <TextObject Name="Text16" Left="85.05" Width="18.9" Height="9.45" Border.Lines="Bottom" Border.Style="Dash" Border.Width="0.25" GrowToBottom="true" CanBreak="false" Text="[DocLines.qte]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="0" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 5pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text19" Left="132.3" Width="37.8" Height="9.45" Border.Lines="Bottom" Border.Style="Dash" Border.Width="0.25" GrowToBottom="true" CanBreak="false" Text="[DocLines.mTTC]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 5pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text40" Left="103.95" Width="28.35" Height="9.45" Border.Lines="Bottom" Border.Style="Dash" Border.Width="0.25" GrowToBottom="true" CanBreak="false" Text="[DocLines.tauxTva] %" Format="Number" Format.UseLocale="true" Format.DecimalDigits="0" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 5pt" Trimming="EllipsisCharacter"/>
      <LineObject Name="Line2" Left="217.35" CanGrow="true" GrowToBottom="true"/>
    </DataBand>
    <PageFooterBand Name="PageFooter1" Top="245.6" Width="170.1" Height="69.5" Guides="18.89999,141.75,47.25,66.15,75.60001,94.5,103.95,122.85,132.3,56.7,47.25,9.45,9.45,9.45,9.45,9.45,9.45,9.45,9.45">
      <TextObject Name="Text22" Left="9.45" Top="28.35" Width="37.8" Height="9.45" Border.Lines="All" Border.Style="Dash" Border.Width="0.25" GrowToBottom="true" Text="Timbre :" HorzAlign="Center" VertAlign="Center" Font="Arial, 5pt"/>
      <TextObject Name="Text23" Left="9.45" Top="37.8" Width="37.8" Height="9.45" Border.Lines="All" Border.Style="Dash" Border.Width="0.25" GrowToBottom="true" Text="Total TTC :" HorzAlign="Center" VertAlign="Center" Font="Arial, 5pt"/>
      <TextObject Name="Text27" Left="47.25" Top="28.35" Width="103.95" Height="9.45" Border.Lines="All" Border.Style="Dash" Border.Width="0.25" GrowToBottom="true" Text="[Docs.Timbre]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 5pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text35" Left="47.25" Top="37.8" Width="103.95" Height="9.45" Border.Lines="All" Border.Style="Dash" Border.Width="0.25" GrowToBottom="true" Text="[Docs.total]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 5pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text20" Left="9.45" Top="9.45" Width="37.8" Height="9.45" Border.Lines="All" Border.Style="Dash" Border.Width="0.25" GrowToBottom="true" CanBreak="false" Text="Total HT :" HorzAlign="Center" VertAlign="Center" Font="Arial, 5pt"/>
      <TextObject Name="Text21" Left="9.45" Top="18.9" Width="37.8" Height="9.45" Border.Lines="All" Border.Style="Dash" Border.Width="0.25" GrowToBottom="true" Text="TVA :" HorzAlign="Center" VertAlign="Center" Font="Arial, 5pt"/>
      <TextObject Name="Text25" Left="47.25" Top="9.45" Width="103.95" Height="9.45" Border.Lines="All" Border.Style="Dash" Border.Width="0.25" GrowToBottom="true" Text="[Docs.TotalHT]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 5pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text26" Left="47.25" Top="18.9" Width="103.95" Height="9.45" Border.Lines="All" Border.Style="Dash" Border.Width="0.25" GrowToBottom="true" Text="[Docs.TotalTVA]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 5pt" Trimming="EllipsisCharacter"/>
    </PageFooterBand>
  </ReportPage>
</Report>
