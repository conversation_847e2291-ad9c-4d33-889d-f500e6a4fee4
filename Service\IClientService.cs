﻿using LearnAPI.Helper;
using LearnAPI.Modal;


namespace LearnAPI.Service
{
    public interface IClientService
    {
        Task<List<Clientmodal>> Getall();
        Task<Clientmodal> Getbycode(string code);
        Task<Clientmodal> GetById(int idt);
        Task<APIResponse> Remove(string code);
        Task<APIResponse> Create(Clientmodal data);

        Task<APIResponse> Update(Clientmodal data,string code);
    }
}
