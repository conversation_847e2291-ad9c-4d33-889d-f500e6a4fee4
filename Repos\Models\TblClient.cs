﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LearnAPI.Repos.Models;

[Table("tbl_client")]
public partial class TblClient
{
    [Key]
    public int idt { get; set; }

   // [StringLength(50)]
    [Unicode(false)]
    public string Code { get; set; } = null!;

    //[StringLength(50)]
    
    public string? rs { get; set; }

    //[StringLength(100)]
    
    public string? adr1 { get; set; }

    //[StringLength(100)]
    
    public string? adr2 { get; set; }

  //  [StringLength(50)]
    
    public string? tel1 { get; set; }

  //  [StringLength(50)]
    
    public string? tel2 { get; set; }

  //  [StringLength(100)]
    
    public string? Email { get; set; }

 //   [StringLength(50)]
    
    public string? mf { get; set; }

  //  [StringLength(50)]
    
    public string? type { get; set; }

    public bool? exo { get; set; }
    public bool? fodec { get; set; }

    public bool? IsActive { get; set; }


}
