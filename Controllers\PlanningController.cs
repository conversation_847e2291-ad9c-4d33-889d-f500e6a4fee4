using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Data.SqlClient;
using System;
using Modal;

namespace Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PlanningController : ControllerBase
    {
        private readonly string _connectionString = "Server=DESKTOP-JM9HQ35\\MSSQLSERVER01;Database=Pointage_DB;Trusted_Connection=True;";

        [HttpGet]
        public IActionResult GetAll()
        {
            try
            {
                var plannings = new List<PlanningModel>();
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("SELECT [Id], [UtilisateurId], [JourSemaine], [HeureDebut], [HeureFin] FROM [Pointage_DB].[dbo].[Plannings]", connection);
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            plannings.Add(new PlanningModel
                            {
                                Id = reader.GetInt32(0),
                                UtilisateurId = reader.GetInt32(1),
                                JourSemaine = reader.IsDBNull(2) ? null : reader.GetString(2),
                                HeureDebut = reader.IsDBNull(3) ? (TimeSpan?)null : reader.GetTimeSpan(3),
                                HeureFin = reader.IsDBNull(4) ? (TimeSpan?)null : reader.GetTimeSpan(4)
                            });
                        }
                    }
                }
                return Ok(plannings);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpGet("{id}")]
        public IActionResult GetById(int id)
        {
            try
            {
                PlanningModel? planning = null;
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("SELECT [Id], [UtilisateurId], [JourSemaine], [HeureDebut], [HeureFin] FROM [Pointage_DB].[dbo].[Plannings] WHERE Id = @Id", connection);
                    command.Parameters.AddWithValue("@Id", id);
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            planning = new PlanningModel
                            {
                                Id = reader.GetInt32(0),
                                UtilisateurId = reader.GetInt32(1),
                                JourSemaine = reader.IsDBNull(2) ? null : reader.GetString(2),
                                HeureDebut = reader.IsDBNull(3) ? (TimeSpan?)null : reader.GetTimeSpan(3),
                                HeureFin = reader.IsDBNull(4) ? (TimeSpan?)null : reader.GetTimeSpan(4)
                            };
                        }
                    }
                }
                if (planning == null) return NotFound();
                return Ok(planning);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpPost]
        public IActionResult Create([FromBody] PlanningModel model)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("INSERT INTO [Pointage_DB].[dbo].[Plannings] ([UtilisateurId], [JourSemaine], [HeureDebut], [HeureFin]) VALUES (@UtilisateurId, @JourSemaine, @HeureDebut, @HeureFin); SELECT SCOPE_IDENTITY();", connection);
                    command.Parameters.AddWithValue("@UtilisateurId", model.UtilisateurId);
                    command.Parameters.AddWithValue("@JourSemaine", (object?)model.JourSemaine ?? DBNull.Value);
                    command.Parameters.AddWithValue("@HeureDebut", (object?)model.HeureDebut ?? DBNull.Value);
                    command.Parameters.AddWithValue("@HeureFin", (object?)model.HeureFin ?? DBNull.Value);
                    connection.Open();
                    var newId = Convert.ToInt32(command.ExecuteScalar());
                    model.Id = newId;
                }
                return Ok(model);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpPut("{id}")]
        public IActionResult Update(int id, [FromBody] PlanningModel model)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("UPDATE [Pointage_DB].[dbo].[Plannings] SET UtilisateurId = @UtilisateurId, JourSemaine = @JourSemaine, HeureDebut = @HeureDebut, HeureFin = @HeureFin WHERE Id = @Id", connection);
                    command.Parameters.AddWithValue("@Id", id);
                    command.Parameters.AddWithValue("@UtilisateurId", model.UtilisateurId);
                    command.Parameters.AddWithValue("@JourSemaine", (object?)model.JourSemaine ?? DBNull.Value);
                    command.Parameters.AddWithValue("@HeureDebut", (object?)model.HeureDebut ?? DBNull.Value);
                    command.Parameters.AddWithValue("@HeureFin", (object?)model.HeureFin ?? DBNull.Value);
                    connection.Open();
                    var rows = command.ExecuteNonQuery();
                    if (rows == 0) return NotFound();
                }
                return Ok(model);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpDelete("{id}")]
        public IActionResult Delete(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("DELETE FROM [Pointage_DB].[dbo].[Plannings] WHERE Id = @Id", connection);
                    command.Parameters.AddWithValue("@Id", id);
                    connection.Open();
                    var rows = command.ExecuteNonQuery();
                    if (rows == 0) return NotFound();
                }
                return Ok();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }
    }
}
