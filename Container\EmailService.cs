﻿using LearnAPI.Modal;
using LearnAPI.Service;
using MailKit.Net.Smtp;
using MailKit.Net.Pop3;
using MailKit.Security;
using Microsoft.Extensions.Options;
using MimeKit;

namespace LearnAPI.Container
{
    public class EmailService : IEmailService
    {
        private readonly EmailSettings emailSettings;

        public EmailService(IOptions<EmailSettings> options)
        {
            this.emailSettings = options.Value;
        }

        public async Task SendEmail(Mailrequest mailrequest)
        {
            var email = new MimeMessage();
          //  email.Sender = MailboxAddress.Parse(emailSettings.Email);
            email.Sender = new MailboxAddress("Mega Chip Solution", emailSettings.Email);
            email.From.Add(new MailboxAddress("Mega Chip Solution", emailSettings.Email));

            email.To.Add(MailboxAddress.Parse(mailrequest.Email));
            email.Subject = mailrequest.Subject;
            var builder = new BodyBuilder();
            builder.HtmlBody = mailrequest.Emailbody;
            email.Body = builder.ToMessageBody();

            using var smtp = new SmtpClient();
            smtp.Connect(emailSettings.Host, emailSettings.Port, SecureSocketOptions.StartTls);
            smtp.Authenticate(emailSettings.Email, emailSettings.Password);
            await smtp.SendAsync(email);
            smtp.Disconnect(true);
        }

        public async Task<List<MimeMessage>> ReceiveEmails()
        {
            var emails = new List<MimeMessage>();

            using var pop3 = new Pop3Client();
            await pop3.ConnectAsync(emailSettings.Host, emailSettings.Port, SecureSocketOptions.SslOnConnect);
            await pop3.AuthenticateAsync(emailSettings.Email, emailSettings.Password);

            for (int i = 0; i < pop3.Count; i++)
            {
                var message = await pop3.GetMessageAsync(i);
                emails.Add(message);
            }

            await pop3.DisconnectAsync(true);
            return emails;
        }
    }
}


/////////////////////
///
//using LearnAPI.Modal;
//using LearnAPI.Service;
//using MailKit.Net.Smtp;
//using MailKit.Security;
//using Microsoft.Extensions.Options;
//using MimeKit;

//namespace LearnAPI.Container
//{
//    public class EmailService : IEmailService
//    {
//        private readonly EmailSettings emailSettings;
//        public EmailService(IOptions<EmailSettings> options)
//        {
//            this.emailSettings = options.Value;
//        }
//        public async Task SendEmail(Mailrequest mailrequest)
//        {
//            var email = new MimeMessage();
//            email.Sender = MailboxAddress.Parse(emailSettings.Email);
//            email.To.Add(MailboxAddress.Parse(mailrequest.Email));
//            email.Subject = mailrequest.Subject;
//            var builder = new BodyBuilder();
//            builder.HtmlBody = mailrequest.Emailbody;
//            email.Body = builder.ToMessageBody();

//            using var smptp = new SmtpClient();
//            smptp.Connect(emailSettings.Host, emailSettings.Port, SecureSocketOptions.StartTls);
//            smptp.Authenticate(emailSettings.Email, emailSettings.Password);
//            await smptp.SendAsync(email);
//            smptp.Disconnect(true);
//        }
//    }
//}
