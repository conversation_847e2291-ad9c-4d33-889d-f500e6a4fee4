<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration Dynamic-Join Employee-Verif</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        .property-calculated {
            background-color: #e3f2fd;
            font-style: italic;
        }
        .security-level-high {
            color: #28a745;
            font-weight: bold;
        }
        .security-level-medium {
            color: #ffc107;
            font-weight: bold;
        }
        .security-level-low {
            color: #dc3545;
            font-weight: bold;
        }
        .json-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="bi bi-diagram-3"></i>
                    Démonstration Dynamic-Join Employee-Verif
                </h1>
                <p class="lead">
                    Cette page démontre l'utilisation de la méthode dynamic-join qui permet de travailler 
                    de manière dynamique avec les tables Employee et Verif en utilisant ExpandoObject.
                </p>
            </div>
        </div>

        <!-- Boutons d'action -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary" id="loadAllEmployees">
                        <i class="bi bi-people"></i> Charger tous les employés
                    </button>
                    <button type="button" class="btn btn-info" id="loadTableStructure">
                        <i class="bi bi-table"></i> Structure des tables
                    </button>
                    <button type="button" class="btn btn-success" id="runDemo">
                        <i class="bi bi-play-circle"></i> Démonstration complète
                    </button>
                </div>
            </div>
        </div>

        <!-- Onglets -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="employees-tab" data-bs-toggle="tab" data-bs-target="#employees" type="button" role="tab">
                    Employés
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="structure-tab" data-bs-toggle="tab" data-bs-target="#structure" type="button" role="tab">
                    Structure
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="update-tab" data-bs-toggle="tab" data-bs-target="#update" type="button" role="tab">
                    Mise à jour
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="console-tab" data-bs-toggle="tab" data-bs-target="#console" type="button" role="tab">
                    Console
                </button>
            </li>
        </ul>

        <div class="tab-content" id="mainTabsContent">
            <!-- Onglet Employés -->
            <div class="tab-pane fade show active" id="employees" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Liste des employés avec données de vérification</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="employeeIdFilter" class="form-label">Filtrer par ID employé:</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="employeeIdFilter" placeholder="ID employé">
                                    <button class="btn btn-outline-secondary" type="button" id="filterEmployee">Filtrer</button>
                                    <button class="btn btn-outline-secondary" type="button" id="clearFilter">Effacer</button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Statistiques:</label>
                                <div id="employeeStats" class="alert alert-info">
                                    Aucune donnée chargée
                                </div>
                            </div>
                        </div>
                        <div class="table-container">
                            <div id="employeesTable">
                                <p class="text-muted">Cliquez sur "Charger tous les employés" pour afficher les données.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Onglet Structure -->
            <div class="tab-pane fade" id="structure" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Structure des tables</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>Tables disponibles:</h6>
                                <div class="list-group">
                                    <button type="button" class="list-group-item list-group-item-action" data-table="employee">
                                        Table Employee
                                    </button>
                                    <button type="button" class="list-group-item list-group-item-action" data-table="verif">
                                        Table Verif
                                    </button>
                                    <button type="button" class="list-group-item list-group-item-action" data-table="pointage">
                                        Table Pointage
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div id="tableStructureDisplay">
                                    <p class="text-muted">Sélectionnez une table pour voir sa structure.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Onglet Mise à jour -->
            <div class="tab-pane fade" id="update" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Mise à jour dynamique</h5>
                    </div>
                    <div class="card-body">
                        <form id="updateForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Informations employé:</h6>
                                    <div class="mb-3">
                                        <label for="employeeId" class="form-label">ID Employé *</label>
                                        <input type="number" class="form-control" id="employeeId" name="employeeId" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="nom" class="form-label">Nom</label>
                                        <input type="text" class="form-control" id="nom" name="Nom">
                                    </div>
                                    <div class="mb-3">
                                        <label for="prenom" class="form-label">Prénom</label>
                                        <input type="text" class="form-control" id="prenom" name="Prenom">
                                    </div>
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="email" name="Email">
                                    </div>
                                    <div class="mb-3">
                                        <label for="poste" class="form-label">Poste</label>
                                        <input type="text" class="form-control" id="poste" name="Poste">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Données de vérification:</h6>
                                    <div class="mb-3">
                                        <label for="verifCode" class="form-label">Code de vérification</label>
                                        <input type="text" class="form-control" id="verifCode" name="VerifCode">
                                    </div>
                                    <div class="mb-3">
                                        <label for="verifPassword" class="form-label">Mot de passe</label>
                                        <input type="password" class="form-control" id="verifPassword" name="VerifPassword">
                                    </div>
                                    <div class="mb-3">
                                        <label for="verifRfid" class="form-label">RFID</label>
                                        <input type="text" class="form-control" id="verifRfid" name="VerifRfid">
                                    </div>
                                    <div class="mb-3">
                                        <label for="verifPhotoFA" class="form-label">Photo FA</label>
                                        <input type="text" class="form-control" id="verifPhotoFA" name="VerifPhotoFA">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-save"></i> Mettre à jour
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="bi bi-arrow-clockwise"></i> Réinitialiser
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Onglet Console -->
            <div class="tab-pane fade" id="console" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Console de débogage</h5>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="clearConsole">
                            Effacer
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="consoleOutput" class="json-display">
                            Console prête...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="DynamicJoinClient.js"></script>
    <script>
        // Instance du client
        const client = new EmployeeVerifDynamicClient();
        let currentEmployeesData = null;

        // Console personnalisée
        const consoleOutput = document.getElementById('consoleOutput');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'text-danger' : 'text-dark';
            consoleOutput.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // Event listeners
        document.addEventListener('DOMContentLoaded', () => {
            // Charger tous les employés
            document.getElementById('loadAllEmployees').addEventListener('click', async () => {
                try {
                    console.log('Chargement de tous les employés...');
                    const data = await client.getAllEmployeesWithVerif();
                    currentEmployeesData = data;
                    client.generateDynamicTable(data.Data, 'employeesTable');
                    updateStats(data);
                } catch (error) {
                    console.error('Erreur:', error);
                }
            });

            // Structure des tables
            document.querySelectorAll('[data-table]').forEach(button => {
                button.addEventListener('click', async () => {
                    const tableName = button.getAttribute('data-table');
                    try {
                        console.log(`Chargement de la structure de la table ${tableName}...`);
                        const structure = await client.getTableStructure(tableName);
                        displayTableStructure(structure);
                    } catch (error) {
                        console.error('Erreur:', error);
                    }
                });
            });

            // Démonstration complète
            document.getElementById('runDemo').addEventListener('click', () => {
                client.demonstrateUsage();
            });

            // Filtrage
            document.getElementById('filterEmployee').addEventListener('click', async () => {
                const employeeId = document.getElementById('employeeIdFilter').value;
                if (employeeId) {
                    try {
                        console.log(`Filtrage par employé ID: ${employeeId}`);
                        const employee = await client.getEmployeeWithVerif(parseInt(employeeId));
                        if (employee) {
                            client.generateDynamicTable([employee], 'employeesTable');
                            updateStats({ Data: [employee], Count: 1 });
                        }
                    } catch (error) {
                        console.error('Erreur:', error);
                    }
                }
            });

            // Effacer filtre
            document.getElementById('clearFilter').addEventListener('click', () => {
                document.getElementById('employeeIdFilter').value = '';
                if (currentEmployeesData) {
                    client.generateDynamicTable(currentEmployeesData.Data, 'employeesTable');
                    updateStats(currentEmployeesData);
                }
            });

            // Effacer console
            document.getElementById('clearConsole').addEventListener('click', () => {
                consoleOutput.innerHTML = 'Console effacée...';
            });
        });

        function updateStats(data) {
            const statsDiv = document.getElementById('employeeStats');
            if (data && data.Data) {
                const total = data.Count;
                const withVerif = data.Data.filter(emp => emp.HasVerificationData).length;
                const withoutVerif = total - withVerif;
                
                statsDiv.innerHTML = `
                    <strong>Total:</strong> ${total} employé(s)<br>
                    <strong>Avec vérification:</strong> ${withVerif}<br>
                    <strong>Sans vérification:</strong> ${withoutVerif}
                `;
            }
        }

        function displayTableStructure(structure) {
            const display = document.getElementById('tableStructureDisplay');
            let html = `<h6>Table: ${structure.TableName}</h6>`;
            html += `<p><strong>Nombre de colonnes:</strong> ${structure.ColumnCount}</p>`;
            html += '<div class="table-responsive">';
            html += '<table class="table table-sm table-striped">';
            html += '<thead><tr><th>Nom</th><th>Type</th><th>Nullable</th><th>Longueur max</th><th>Position</th></tr></thead>';
            html += '<tbody>';
            
            structure.Columns.forEach(col => {
                html += `<tr>
                    <td><code>${col.ColumnName}</code></td>
                    <td><span class="badge bg-secondary">${col.DataType}</span></td>
                    <td>${col.IsNullable ? '✓' : '✗'}</td>
                    <td>${col.MaxLength || '-'}</td>
                    <td>${col.Position}</td>
                </tr>`;
            });
            
            html += '</tbody></table></div>';
            display.innerHTML = html;
        }
    </script>
</body>
</html>
