using Tests;

namespace TestRunner
{
    /// <summary>
    /// Programme simple pour exécuter les tests Dynamic-Join
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("=== PROGRAMME DE TEST DYNAMIC-JOIN ===");
            Console.WriteLine("Ce programme teste les fonctionnalités dynamic-join du contrôleur EmployeeVerif\n");

            try
            {
                // Exécuter tous les tests
                bool allTestsPassed = await EmployeeVerifDynamicJoinTests.ExecuteTests();

                if (allTestsPassed)
                {
                    Console.WriteLine("\n🎉 TOUS LES TESTS ONT RÉUSSI !");
                    Environment.Exit(0);
                }
                else
                {
                    Console.WriteLine("\n❌ CERTAINS TESTS ONT ÉCHOUÉ");
                    Console.WriteLine("Vérifiez la configuration de la base de données et les chaînes de connexion.");
                    Environment.Exit(1);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n💥 ERREUR CRITIQUE: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Environment.Exit(1);
            }
        }
    }
}
