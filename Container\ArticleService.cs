﻿using AutoMapper;
using LearnAPI.Helper;
using LearnAPI.Modal;
using LearnAPI.Repos;
using LearnAPI.Repos.Models;
using LearnAPI.Service;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace LearnAPI.Container
{
    public class ArticleService : IArticleService
    {
        private readonly LearndataContext context;
        private readonly IMapper mapper;
        private readonly ILogger<ArticleService> logger;

        public ArticleService(LearndataContext context, IMapper mapper, ILogger<ArticleService> logger)
        {
            this.context = context;
            this.mapper = mapper;
            this.logger = logger;
        }
        
        public async Task<APIResponse> Create(Articlemodal data)
        {
            APIResponse response = new APIResponse();
            try
            {
                this.logger.LogInformation("Create Begins");
                TblArticle _article = this.mapper.Map<Articlemodal, TblArticle>(data);
                await this.context.TblArticles.AddAsync(_article);
                await this.context.SaveChangesAsync();
                response.ResponseCode = 201;
                response.Result = "pass";
            }
            catch (Exception ex)
            {
                response.ResponseCode = 400;
                response.Message = ex.Message;
                this.logger.LogError(ex.Message, ex);
            }
            return response;
        }
        

        public async Task<List<Articlemodal>> Getall()
        {
            List<Articlemodal> _response = new List<Articlemodal>();
            var _data = await this.context.TblArticles.ToListAsync();
            if (_data != null)
            {
                _response = this.mapper.Map<List<TblArticle>, List<Articlemodal>>(_data);
            }
            return _response;
        }

        public async Task<Articlemodal> Getbycode(string code)
        {
            Articlemodal _response = new Articlemodal();

            // Convert the string 'code' to an int before passing to FindAsync
            if (int.TryParse(code, out int parsedCode))
            {
                var _data = await this.context.TblArticles.FindAsync(parsedCode);
                if (_data != null)
                {
                    _response = this.mapper.Map<TblArticle, Articlemodal>(_data);
                }
            }
            return _response;
        }

        public async Task<Articlemodal> GetById(int idt)
        {
            Articlemodal _response = new Articlemodal();
            var _data = await this.context.TblArticles.FindAsync(idt);
            if (_data != null)
            {
                _response = this.mapper.Map<TblArticle, Articlemodal>(_data);
            }
            return _response;
        }

        public async Task<APIResponse> Remove(string code)
        {
            APIResponse response = new APIResponse();

            // Convert the string 'code' to an int before passing to FindAsync
            if (int.TryParse(code, out int parsedCode))
            {
                try
                {
                    var _article = await this.context.TblArticles.FindAsync(parsedCode);
                    if (_article != null)
                    {
                        this.context.TblArticles.Remove(_article);
                        await this.context.SaveChangesAsync();
                        response.ResponseCode = 200;
                        response.Result = "pass";
                    }
                    else
                    {
                        response.ResponseCode = 404;
                        response.Message = "Data not found";
                    }
                }
                catch (Exception ex)
                {
                    response.ResponseCode = 400;
                    response.Message = ex.Message;
                    this.logger.LogError(ex.Message, ex);
                }
            }
            else
            {
                response.ResponseCode = 400;
                response.Message = "Invalid code format. Code must be an integer.";
            }

            return response;
        }

        public async Task<APIResponse> Update(Articlemodal data, string code)
        {
            APIResponse response = new APIResponse();

            // Convert the string 'code' to an int before passing to FindAsync
            if (int.TryParse(code, out int parsedCode))
            {
                try
                {
                    var _article = await this.context.TblArticles.FindAsync(parsedCode);
                    if (_article != null)
                    {
                        _article.des = data.des;
                        _article.paHT = data.paHT;
                        _article.paTTC = data.paTTC;
                        _article.pvHT = data.pvHT;
                        _article.pvTTC = data.pvTTC;
                        _article.marge = data.marge;
                        _article.qte = data.qte;
                        _article.type = data.type;

                        _article.emplacement = data.emplacement;
                        _article.depo = data.depo;
                        _article.fodec = data.fodec;
                        _article.mntFodec = data.mntFodec;

                        _article.idtFournisseur = data.idtFournisseur;
                        _article.codeFournisseur = data.codeFournisseur;
                        _article.idfFamille = data.idfFamille;
                        _article.codeFamille = data.codeFamille;
                        _article.idtTva = data.idtTva;
                        _article.tauxTva = data.tauxTva;


                        _article.cMP = data.cMP;
                        _article.dPA = data.dPA;
                        _article.margeP = data.margeP;
                        _article.baseCalcul = data.baseCalcul;
                        _article.typeArticle = data.typeArticle;
                        _article.unité = data.unité;

                        _article.codeAbarre = data.codeAbarre;
                        _article.refFR = data.refFR;
                        _article.remisFR = data.remisFR;

                        

                        _article.IsActive = data.IsActive;

                        await this.context.SaveChangesAsync();
                        response.ResponseCode = 200;
                        response.Result = "pass";
                    }
                    else
                    {
                        response.ResponseCode = 404;
                        response.Message = "Data not found";
                    }
                }
                catch (Exception ex)
                {
                    response.ResponseCode = 400;
                    response.Message = ex.Message;
                    this.logger.LogError(ex.Message, ex);
                }
            }
            else
            {
                response.ResponseCode = 400;
                response.Message = "Invalid code format. Code must be an integer.";
            }

            return response;
        }
    }
}
