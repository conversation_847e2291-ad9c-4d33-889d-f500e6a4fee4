﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LearnAPI.Repos.Models;

[Table("tbl_famille")]
public partial class TblFamille
{
    [Key]
    public int idf { get; set; } 

    [Required]
    [MaxLength(100)]
    public string nomf { get; set; }

    
    [StringLength(50)]
    [Unicode(false)]
    public string Code { get; set; } = null!;


    public bool? IsActive { get; set; }



}
