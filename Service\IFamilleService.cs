﻿using LearnAPI.Helper;
using LearnAPI.Modal;


namespace LearnAPI.Service
{
    public interface IFamilleService
    {
        Task<List<Famillemodal>> Getall();
        Task<Famillemodal> Getbycode(string code);
        Task<Famillemodal> GetById(int idt);
        Task<APIResponse> Remove(string code);
        Task<APIResponse> Create(Famillemodal data);

        Task<APIResponse> Update(Famillemodal data,string code);
    }
}
