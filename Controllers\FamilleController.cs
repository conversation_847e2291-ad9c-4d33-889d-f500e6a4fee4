﻿using Microsoft.AspNetCore.Mvc;


using Microsoft.AspNetCore.Authorization;
using ClosedXML.Excel;
using LearnAPI.Modal;
using LearnAPI.Service;
using Microsoft.AspNetCore.RateLimiting;
using System.Data;
using LearnAPI.Container;

namespace LearnAPI.Controllers
{
    //[AllowAnonymous]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

   
        public class FamilleController : ControllerBase
    {
            private readonly IFamilleService service;
            private readonly IWebHostEnvironment environment;
            public FamilleController(IFamilleService service, IWebHostEnvironment environment)
            {
                this.service = service;
                this.environment = environment;
            }

            //[AllowAnonymous]
            // [EnableCors("corspolicy1")]
            [HttpGet("GetAll")]
            public async Task<IActionResult> GetAll()
            {
                var data = await this.service.Getall();
                if (data == null)
                {
                    return NotFound();
                }
                return Ok(data);
            }

        [DisableRateLimiting]

            [HttpGet("Getbycode")]
            public async Task<IActionResult> Getbycode(string code)
            {
                var data = await this.service.Getbycode(code);
                if (data == null)
                {
                    return NotFound();
                }
                return Ok(data);
            }

            [HttpPost("Create")]
            public async Task<IActionResult> Create(Famillemodal _data)
            {
                var data = await this.service.Create(_data);
                return Ok(data);
            }
            [HttpPut("Update")]
            public async Task<IActionResult> Update(Famillemodal _data, string code)
            {
                var data = await this.service.Update(_data, code);
                return Ok(data);
            }

            [HttpDelete("Remove")]
            public async Task<IActionResult> Remove(string code)
            {
                var data = await this.service.Remove(code);
                return Ok(data);
            }

            [AllowAnonymous]
            [HttpGet("Exportexcel")]
            public async Task<IActionResult> Exportexcel()
            {
            try
            {
                string Filepath = GetFilepath();
                string excelpath = Filepath + "\\familleinfo.xlsx";
                DataTable dt = new DataTable();
                dt.Columns.Add("nomf", typeof(string));
                dt.Columns.Add("Code", typeof(string));

            
                var data = await this.service.Getall();
                    if (data != null && data.Count > 0)
                    {
                        data.ForEach(item =>
                        {
                            dt.Rows.Add(item.nomf, item.Code);
                        });
                    }
                    using (XLWorkbook wb = new XLWorkbook())
                    {
                        wb.AddWorksheet(dt, "Famille Info");
                        using (MemoryStream stream = new MemoryStream())
                        {
                            wb.SaveAs(stream);

                            if (System.IO.File.Exists(excelpath))
                            {
                                System.IO.File.Delete(excelpath);
                            }
                            wb.SaveAs(excelpath);

                            return File(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Famille.xlsx");
                        }
                    }
                }
                catch (Exception ex)
                {
                    return NotFound();
                }
            }

            [NonAction]
            private string GetFilepath()
            {
                return this.environment.WebRootPath + "\\Export";
            }
        }
    }
