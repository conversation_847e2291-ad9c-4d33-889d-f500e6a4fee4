﻿using Microsoft.AspNetCore.Mvc;


using Microsoft.AspNetCore.Authorization;
using ClosedXML.Excel;
using LearnAPI.Modal;
using LearnAPI.Service;
using Microsoft.AspNetCore.RateLimiting;
using System.Data;

namespace LearnAPI.Controllers
{
    //[AllowAnonymous]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ArticleController : ControllerBase
    {

        private readonly IArticleService service;
        private readonly IWebHostEnvironment environment;
        public ArticleController(IArticleService service, IWebHostEnvironment environment)
        {
            this.service = service;
            this.environment = environment;
        }
        [AllowAnonymous]
        [DisableRateLimiting]
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAll()
        {
            var data = await this.service.Getall();
            if (data == null)
            {
                return NotFound();
            }
            return Ok(data);
        }

        [DisableRateLimiting]
        [HttpGet("Getbycode")]
        public async Task<IActionResult> Getbycode(string code)
        {
            var data = await this.service.Getbycode(code);
            if (data == null)
            {
                return NotFound();
            }
            return Ok(data);
        }

        [HttpPost("Create")]
        public async Task<IActionResult> Create(Articlemodal _data)
        {
            var data = await this.service.Create(_data);
            return Ok(data);
        }
        [HttpPut("Update")]
        public async Task<IActionResult> Update(Articlemodal _data, string code)
        {
            var data = await this.service.Update(_data, code);
            return Ok(data);
        }

        [HttpDelete("Remove")]
        public async Task<IActionResult> Remove(string code)
        {
            var data = await this.service.Remove(code);
            return Ok(data);
        }



    [HttpGet("Exportexcel")]
    public async Task<IActionResult> Exportexcel()
    {
        try
        {
            string Filepath = GetFilepath();
            string excelpath = Filepath + "\\articleinfo.xlsx";
            DataTable dt = new DataTable();
            dt.Columns.Add("Code", typeof(string));
            dt.Columns.Add("des", typeof(string));
            dt.Columns.Add("paHT", typeof(decimal));
            dt.Columns.Add("paTTC", typeof(decimal));
            dt.Columns.Add("pvHT", typeof(decimal));
            dt.Columns.Add("pvTTC", typeof(decimal));
            dt.Columns.Add("marge", typeof(decimal));
            dt.Columns.Add("qte", typeof(decimal));
            dt.Columns.Add("type", typeof(string));

            dt.Columns.Add("emplacement", typeof(string));
            dt.Columns.Add("depo", typeof(string));
            dt.Columns.Add("fodec", typeof(Boolean));
            dt.Columns.Add("mntFodec", typeof(decimal));

            dt.Columns.Add("cMP", typeof(decimal));
            dt.Columns.Add("dPA", typeof(decimal));
            dt.Columns.Add("margeP", typeof(decimal));
            dt.Columns.Add("baseCalcul", typeof(string));
            dt.Columns.Add("typeArticle", typeof(string));
            dt.Columns.Add("unité", typeof(string));

            dt.Columns.Add("codeAbarre", typeof(string));
            dt.Columns.Add("refFR", typeof(string));
            dt.Columns.Add("remisFR", typeof(decimal));


        var data = await this.service.Getall();
            if (data != null && data.Count > 0)
            {
                data.ForEach(item =>
                {
                    dt.Rows.Add(item.Code, item.des, item.paHT, item.paTTC, item.pvHT, item.pvTTC, item.marge, item.qte, item.type, item.emplacement, item.depo, item.fodec, item.mntFodec, item.cMP, item.dPA, item.margeP, item.baseCalcul, item.typeArticle, item.unité,item.codeAbarre, item.refFR ,item.remisFR);
                });
            }
            using (XLWorkbook wb = new XLWorkbook())
            {
                wb.AddWorksheet(dt, "Article Info");
                using (MemoryStream stream = new MemoryStream())
                {
                    wb.SaveAs(stream);

                    if (System.IO.File.Exists(excelpath))
                    {
                        System.IO.File.Delete(excelpath);
                    }
                    wb.SaveAs(excelpath);

                    return File(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Article.xlsx");
                }
            }
        }
        catch (Exception ex)
        {
            return NotFound();
        }
    }

    [NonAction]
    private string GetFilepath()
    {
        return this.environment.WebRootPath + "\\Export";
    }
}
}
        
