using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;

namespace LearnAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MetadataController : ControllerBase
    {
        private readonly IConfiguration _configuration;

        public MetadataController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        [HttpGet("columns/{tableName}")]
        public async Task<IActionResult> GetTableColumns(string tableName)
        {
            try
            {
                var connectionString = _configuration.GetConnectionString("apicon");
                var columns = new List<object>();
                
                // Mapper le nom de la table
                var sqlTableName = tableName.ToLower() == "periode" ? "tbl_periode" : tableName;
                
                Console.WriteLine($"🔍 Récupération des colonnes pour: {sqlTableName}");

                var query = @"
                    SELECT 
                        COLUMN_NAME as columnName,
                        DATA_TYPE as dataType,
                        CASE WHEN IS_NULLABLE = 'YES' THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END as isNullable,
                        CASE WHEN EXISTS (
                            SELECT 1 FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                            INNER JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc 
                                ON kcu.CONSTRAINT_NAME = tc.CONSTRAINT_NAME
                            WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' 
                                AND kcu.TABLE_NAME = c.TABLE_NAME 
                                AND kcu.COLUMN_NAME = c.COLUMN_NAME
                        ) THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END as isPrimaryKey,
                        CASE WHEN EXISTS (
                            SELECT 1 FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                            INNER JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc 
                                ON kcu.CONSTRAINT_NAME = tc.CONSTRAINT_NAME
                            WHERE tc.CONSTRAINT_TYPE = 'FOREIGN KEY' 
                                AND kcu.TABLE_NAME = c.TABLE_NAME 
                                AND kcu.COLUMN_NAME = c.COLUMN_NAME
                        ) THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END as isForeignKey,
                        CHARACTER_MAXIMUM_LENGTH as maxLength,
                        ORDINAL_POSITION as position
                    FROM INFORMATION_SCHEMA.COLUMNS c
                    WHERE c.TABLE_NAME = @tableName
                    ORDER BY c.ORDINAL_POSITION";

                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@tableName", sqlTableName);
                        
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var columnName = reader["columnName"].ToString();
                                
                                columns.Add(new
                                {
                                    columnName = columnName,
                                    dataType = reader["dataType"].ToString(),
                                    isNullable = Convert.ToBoolean(reader["isNullable"]),
                                    isPrimaryKey = Convert.ToBoolean(reader["isPrimaryKey"]),
                                    isForeignKey = Convert.ToBoolean(reader["isForeignKey"]),
                                    maxLength = reader["maxLength"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["maxLength"]),
                                    position = Convert.ToInt32(reader["position"]),
                                    // Enrichissement automatique
                                    displayName = FormatDisplayName(columnName),
                                    inputType = InferInputType(reader["dataType"].ToString(), columnName),
                                    group = InferGroup(columnName),
                                    isVisible = !IsSystemColumn(columnName),
                                    isRequired = !Convert.ToBoolean(reader["isNullable"]) || Convert.ToBoolean(reader["isPrimaryKey"])
                                });
                            }
                        }
                    }
                }

                Console.WriteLine($"✅ {columns.Count} colonnes trouvées pour {sqlTableName}");
                return Ok(columns);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur: {ex.Message}");
                return BadRequest($"Erreur: {ex.Message}");
            }
        }

        [HttpPost("columns/{tableName}")]
        public async Task<IActionResult> UpdateTableMetadata(string tableName, [FromBody] List<ColumnMetadata> columns)
        {
            try
            {
                // Validation des données d'entrée
                if (string.IsNullOrWhiteSpace(tableName))
                {
                    return BadRequest(new { message = "Le nom de la table est requis." });
                }

                if (columns == null || !columns.Any())
                {
                    return BadRequest(new { message = "Les métadonnées des colonnes sont requises." });
                }

                // Validation de chaque colonne
                var validationErrors = new List<string>();
                foreach (var column in columns)
                {
                    if (string.IsNullOrWhiteSpace(column.ColumnName))
                        validationErrors.Add("Le nom de colonne ne peut pas être vide.");

                    if (string.IsNullOrWhiteSpace(column.DataType))
                        validationErrors.Add($"Le type de données est requis pour la colonne '{column.ColumnName}'.");
                }

                if (validationErrors.Any())
                {
                    return BadRequest(new { message = "Erreurs de validation", errors = validationErrors });
                }

                // TODO: Implémenter la logique de sauvegarde en base de données
                // Pour l'instant, on simule une sauvegarde réussie

                // Exemple de ce qui pourrait être fait :
                // 1. Vérifier si la table existe
                // 2. Sauvegarder les métadonnées dans une table de configuration
                // 3. Mettre à jour le cache si nécessaire

                var response = new
                {
                    message = "Métadonnées mises à jour avec succès",
                    tableName = tableName,
                    columnsUpdated = columns.Count,
                    timestamp = DateTime.UtcNow
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    message = "Erreur lors de la mise à jour des métadonnées",
                    error = ex.Message
                });
            }
        }

        [HttpPost("columns/{tableName}/validate")]
        public async Task<IActionResult> ValidateTableStructure(string tableName, [FromBody] List<ColumnMetadata> expectedColumns)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(tableName))
                {
                    return BadRequest(new { message = "Le nom de la table est requis." });
                }

                // TODO: Récupérer la structure actuelle de la table depuis la base de données
                // et la comparer avec les colonnes attendues

                var actualColumns = await GetTableColumnsFromDatabase(tableName);
                var differences = CompareTableStructures(expectedColumns, actualColumns);

                var response = new
                {
                    tableName = tableName,
                    isValid = !differences.Any(),
                    differences = differences,
                    timestamp = DateTime.UtcNow
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    message = "Erreur lors de la validation de la structure",
                    error = ex.Message
                });
            }
        }

        private async Task<List<ColumnMetadata>> GetTableColumnsFromDatabase(string tableName)
        {
            // TODO: Implémenter la récupération réelle depuis la base de données
            // Pour l'instant, retourner une liste vide
            return new List<ColumnMetadata>();
        }

        private List<object> CompareTableStructures(List<ColumnMetadata> expected, List<ColumnMetadata> actual)
        {
            var differences = new List<object>();

            // Colonnes manquantes
            var missingColumns = expected.Where(e => !actual.Any(a => a.ColumnName.Equals(e.ColumnName, StringComparison.OrdinalIgnoreCase))).ToList();
            foreach (var missing in missingColumns)
            {
                differences.Add(new { type = "missing", columnName = missing.ColumnName, message = "Colonne manquante dans la base de données" });
            }

            // Colonnes supplémentaires
            var extraColumns = actual.Where(a => !expected.Any(e => e.ColumnName.Equals(a.ColumnName, StringComparison.OrdinalIgnoreCase))).ToList();
            foreach (var extra in extraColumns)
            {
                differences.Add(new { type = "extra", columnName = extra.ColumnName, message = "Colonne supplémentaire dans la base de données" });
            }

            // Différences de type
            foreach (var expectedCol in expected)
            {
                var actualCol = actual.FirstOrDefault(a => a.ColumnName.Equals(expectedCol.ColumnName, StringComparison.OrdinalIgnoreCase));
                if (actualCol != null && !actualCol.DataType.Equals(expectedCol.DataType, StringComparison.OrdinalIgnoreCase))
                {
                    differences.Add(new {
                        type = "type_mismatch",
                        columnName = expectedCol.ColumnName,
                        expected = expectedCol.DataType,
                        actual = actualCol.DataType,
                        message = "Type de données différent"
                    });
                }
            }

            return differences;
        }

        private string FormatDisplayName(string columnName)
        {
            return columnName switch
            {
                "id" => "ID",
                "employee_id" => "ID Employé",
                "type_periode_id" => "Type Période",
                "heure_debut_matin" => "Début Matin",
                "heure_fin_matin" => "Fin Matin",
                "heure_debut_apresmidi" => "Début Après-midi",
                "heure_fin_apresmidi" => "Fin Après-midi",
                "duree_totale" => "Durée Totale",
                "nom" => "Nom",
                "prenom" => "Prénom",
                _ => columnName.Replace("_", " ").ToTitleCase()
            };
        }

        private string InferInputType(string dataType, string columnName)
        {
            var lowerDataType = dataType.ToLower();
            var lowerColumnName = columnName.ToLower();

            if (lowerColumnName.Contains("email")) return "email";
            if (lowerColumnName.Contains("date") || lowerDataType.Contains("date")) return "date";
            if (lowerColumnName.Contains("time") || lowerDataType.Contains("time")) return "text";
            if (lowerDataType.Contains("bit")) return "checkbox";
            if (lowerDataType.Contains("int") || lowerDataType.Contains("decimal")) return "number";
            if (lowerColumnName.EndsWith("_id") && lowerColumnName != "id") return "select";
            
            return "text";
        }

        private string InferGroup(string columnName)
        {
            var lowerColumnName = columnName.ToLower();

            if (lowerColumnName.Contains("nom") || lowerColumnName.Contains("prenom")) return "personal";
            if (lowerColumnName.Contains("email") || lowerColumnName.Contains("telephone")) return "contact";
            if (lowerColumnName.Contains("periode") || lowerColumnName.Contains("employee")) return "work";
            
            return "general";
        }

        private bool IsSystemColumn(string columnName)
        {
            var systemColumns = new[] { "created_at", "updated_at", "deleted_at" };
            return systemColumns.Any(sys => columnName.ToLower().Contains(sys));
        }
    }

    public class ColumnMetadata
    {
        public string ColumnName { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public bool IsNullable { get; set; }
        public bool IsPrimaryKey { get; set; }
        public bool IsForeignKey { get; set; }
        public int? MaxLength { get; set; }
        public int Position { get; set; }
        public string DisplayName { get; set; } = string.Empty;
        public string InputType { get; set; } = "text";
        public string Group { get; set; } = "general";
        public bool IsVisible { get; set; } = true;
        public bool IsRequired { get; set; }
        public string? DefaultValue { get; set; }
        public string? Description { get; set; }
        public bool IsReadOnly { get; set; }
        public string? ValidationPattern { get; set; }
        public string? PlaceholderText { get; set; }
    }

    public static class StringExtensions
    {
        public static string ToTitleCase(this string input)
        {
            if (string.IsNullOrEmpty(input)) return input;

            var words = input.Split(' ', '_', '-');
            for (int i = 0; i < words.Length; i++)
            {
                if (words[i].Length > 0)
                {
                    words[i] = char.ToUpper(words[i][0]) + words[i].Substring(1).ToLower();
                }
            }
            return string.Join(" ", words);
        }
    }
}