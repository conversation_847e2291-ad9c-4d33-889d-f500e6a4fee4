using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Data.SqlClient;
using System;
using Modal;

namespace Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PeriodeController : ControllerBase
    {
        private readonly string _connectionString = "Server=DESKTOP-JM9HQ35\\MSSQLSERVER01;Database=Pointage_DB;Trusted_Connection=True;";

        [HttpGet]
        public IActionResult GetAll()
        {
            try
            {
                var periodes = new List<dynamic>();
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand(@"SELECT p.[id], p.[heure_debut_matin], p.[heure_fin_matin], p.[heure_debut_apresmidi], p.[heure_fin_apresmidi], p.[duree_totale], p.[type_periode_id], t.[nom_type] FROM [Pointage_DB].[dbo].[tbl_periode] p LEFT JOIN [Pointage_DB].[dbo].[type_periode] t ON p.type_periode_id = t.id", connection);
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            periodes.Add(new {
                                id = reader.GetInt32(0),
                                heure_debut_matin = reader.IsDBNull(1) ? (TimeSpan?)null : reader.GetTimeSpan(1),
                                heure_fin_matin = reader.IsDBNull(2) ? (TimeSpan?)null : reader.GetTimeSpan(2),
                                heure_debut_apresmidi = reader.IsDBNull(3) ? (TimeSpan?)null : reader.GetTimeSpan(3),
                                heure_fin_apresmidi = reader.IsDBNull(4) ? (TimeSpan?)null : reader.GetTimeSpan(4),
                                duree_totale = reader.IsDBNull(5) ? (TimeSpan?)null : reader.GetTimeSpan(5),
                                type_periode_id = reader.IsDBNull(6) ? (int?)null : reader.GetInt32(6),
                                nom_type = reader.IsDBNull(7) ? null : reader.GetString(7)
                            });
                        }
                    }
                }
                return Ok(periodes);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpGet("{id}")]
        public IActionResult GetById(int id)
        {
            try
            {
                PeriodeModel? periode = null;
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("SELECT [id], [heure_debut_matin], [heure_fin_matin], [heure_debut_apresmidi], [heure_fin_apresmidi], [duree_totale], [type_periode_id] FROM [Pointage_DB].[dbo].[tbl_periode] WHERE id = @id", connection);
                    command.Parameters.AddWithValue("@id", id);
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            periode = new PeriodeModel
                            {
                                id = reader.GetInt32(0),
                                heure_debut_matin = reader.IsDBNull(1) ? (TimeSpan?)null : reader.GetTimeSpan(1),
                                heure_fin_matin = reader.IsDBNull(2) ? (TimeSpan?)null : reader.GetTimeSpan(2),
                                heure_debut_apresmidi = reader.IsDBNull(3) ? (TimeSpan?)null : reader.GetTimeSpan(3),
                                heure_fin_apresmidi = reader.IsDBNull(4) ? (TimeSpan?)null : reader.GetTimeSpan(4),
                                duree_totale = reader.IsDBNull(5) ? (TimeSpan?)null : reader.GetTimeSpan(5),
                                type_periode_id = reader.IsDBNull(6) ? (int?)null : reader.GetInt32(6)
                            };
                        }
                    }
                }
                if (periode == null) return NotFound();
                return Ok(periode);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        //     [HttpPost("dynamic")]
        // public IActionResult CreateDynamic([FromBody] Dictionary<string, object> data)
        // {
        //     try
        //     {
        //         // Récupérer dynamiquement les colonnes de la table (hors id)
        //         var columns = new List<string>();
        //         using (var connection = new SqlConnection(_connectionString))
        //         {
        //             var cmdCols = new SqlCommand("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tbl_periode' AND COLUMN_NAME <> 'id'", connection);
        //             connection.Open();
        //             using (var reader = cmdCols.ExecuteReader())
        //             {
        //                 while (reader.Read())
        //                 {
        //                     columns.Add(reader.GetString(0));
        //                 }
        //             }
        //             // Construction dynamique de la requête
        //             var insertCols = new List<string>();
        //             var insertParams = new List<string>();
        //             var sqlCommand = new SqlCommand();
        //             foreach (var col in columns)
        //             {
        //                 if (data.ContainsKey(col))
        //                 {
        //                     insertCols.Add($"[{col}]");
        //                     insertParams.Add($"@{col}");
        //                     sqlCommand.Parameters.AddWithValue($"@{col}", data[col] ?? DBNull.Value);
        //                 }
        //             }
        //             if (insertCols.Count == 0)
        //                 return BadRequest("Aucune donnée à insérer.");
        //             var sql = $"INSERT INTO [Pointage_DB].[dbo].[tbl_periode] ({string.Join(",", insertCols)}) VALUES ({string.Join(",", insertParams)}); SELECT SCOPE_IDENTITY();";
        //             sqlCommand.CommandText = sql;
        //             sqlCommand.Connection = connection;
        //             var newId = Convert.ToInt32(sqlCommand.ExecuteScalar());
        //             data["id"] = newId;
        //         }
        //         return Ok(data);
        //     }
        //     catch (Exception ex)
        //     {
        //         return StatusCode(500, $"Erreur serveur: {ex.Message}");
        //     }
        // }

        [HttpPost]
        public IActionResult Create([FromBody] PeriodeModel model)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("INSERT INTO [Pointage_DB].[dbo].[tbl_periode] ([heure_debut_matin], [heure_fin_matin], [heure_debut_apresmidi], [heure_fin_apresmidi], [duree_totale], [type_periode_id]) VALUES (@heure_debut_matin, @heure_fin_matin, @heure_debut_apresmidi, @heure_fin_apresmidi, @duree_totale, @type_periode_id); SELECT SCOPE_IDENTITY();", connection);
                    command.Parameters.AddWithValue("@heure_debut_matin", (object?)model.heure_debut_matin ?? DBNull.Value);
                    command.Parameters.AddWithValue("@heure_fin_matin", (object?)model.heure_fin_matin ?? DBNull.Value);
                    command.Parameters.AddWithValue("@heure_debut_apresmidi", (object?)model.heure_debut_apresmidi ?? DBNull.Value);
                    command.Parameters.AddWithValue("@heure_fin_apresmidi", (object?)model.heure_fin_apresmidi ?? DBNull.Value);
                    command.Parameters.AddWithValue("@duree_totale", (object?)model.duree_totale ?? DBNull.Value);
                    command.Parameters.AddWithValue("@type_periode_id", (object?)model.type_periode_id ?? DBNull.Value);
                    connection.Open();
                    var newId = Convert.ToInt32(command.ExecuteScalar());
                    model.id = newId;
                }
                return Ok(model);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpPut("{id}")]
        public IActionResult Update(int id, [FromBody] PeriodeModel model)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("UPDATE [Pointage_DB].[dbo].[tbl_periode] SET heure_debut_matin = @heure_debut_matin, heure_fin_matin = @heure_fin_matin, heure_debut_apresmidi = @heure_debut_apresmidi, heure_fin_apresmidi = @heure_fin_apresmidi, duree_totale = @duree_totale, type_periode_id = @type_periode_id WHERE id = @id", connection);
                    command.Parameters.AddWithValue("@id", id);
                    command.Parameters.AddWithValue("@heure_debut_matin", (object?)model.heure_debut_matin ?? DBNull.Value);
                    command.Parameters.AddWithValue("@heure_fin_matin", (object?)model.heure_fin_matin ?? DBNull.Value);
                    command.Parameters.AddWithValue("@heure_debut_apresmidi", (object?)model.heure_debut_apresmidi ?? DBNull.Value);
                    command.Parameters.AddWithValue("@heure_fin_apresmidi", (object?)model.heure_fin_apresmidi ?? DBNull.Value);
                    command.Parameters.AddWithValue("@duree_totale", (object?)model.duree_totale ?? DBNull.Value);
                    command.Parameters.AddWithValue("@type_periode_id", (object?)model.type_periode_id ?? DBNull.Value);
                    connection.Open();
                    var rows = command.ExecuteNonQuery();
                    if (rows == 0) return NotFound();
                }
                return Ok(model);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }


        [HttpGet("byname/{nom}")]
public IActionResult GetByName(string nom)
{
    try
    {
        using (var connection = new SqlConnection(_connectionString))
        {
            var command = new SqlCommand("SELECT id, nom_type FROM type_periode WHERE nom_type = @nom", connection);
            command.Parameters.AddWithValue("@nom", nom);
            connection.Open();
            using (var reader = command.ExecuteReader())
            {
                if (reader.Read())
                {
                    var typePeriode = new TypePeriodeModel
                    {
                        id = reader.GetInt32(0),
                        nom_type = reader.GetString(1)
                    };
                    return Ok(typePeriode);
                }
            }
        }

        return NotFound();
    }
    catch (Exception ex)
    {
        return StatusCode(500, $"Erreur serveur : {ex.Message}");
    }
}


        [HttpDelete("{id}")]
        public IActionResult Delete(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("DELETE FROM [Pointage_DB].[dbo].[tbl_periode] WHERE id = @id", connection);
                    command.Parameters.AddWithValue("@id", id);
                    connection.Open();
                    var rows = command.ExecuteNonQuery();
                    if (rows == 0) return NotFound();
                }
                return Ok();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }
    }
}
