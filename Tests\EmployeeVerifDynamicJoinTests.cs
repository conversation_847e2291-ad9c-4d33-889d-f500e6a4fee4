using Microsoft.AspNetCore.Mvc;
using System.Dynamic;
using System.Text;
using System.Text.Json;
using Controllers;
using System.Data.SqlClient;

namespace Tests
{
    /// <summary>
    /// Tests unitaires pour les méthodes dynamic-join du contrôleur EmployeeVerif
    /// Ces tests peuvent être exécutés indépendamment sans serveur web
    /// </summary>
    public class EmployeeVerifDynamicJoinTests
    {
        private readonly EmployeeVerifController _controller;

        public EmployeeVerifDynamicJoinTests()
        {
            _controller = new EmployeeVerifController();
        }

        /// <summary>
        /// Test pour vérifier que la méthode GetEmployeeVerifDynamicJoin retourne un résultat valide
        /// </summary>
        public async Task<bool> TestGetDynamicJoin_ShouldReturnValidResult()
        {
            try
            {
                // Act
                var result = await _controller.GetEmployeeVerifDynamicJoin();

                // Assert
                if (result is OkObjectResult okResult)
                {
                    var data = okResult.Value;
                    Console.WriteLine("✓ GetDynamicJoin retourne un résultat valide");
                    Console.WriteLine($"  Type de données: {data?.GetType().Name}");
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ GetDynamicJoin n'a pas retourné un OkObjectResult");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur dans TestGetDynamicJoin: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test pour vérifier que la méthode GetEmployeeVerifDynamicJoin avec un ID spécifique fonctionne
        /// </summary>
        public async Task<bool> TestGetDynamicJoin_WithEmployeeId_ShouldWork()
        {
            try
            {
                // Arrange
                int employeeId = 1; // Test avec ID 1

                // Act
                var result = await _controller.GetEmployeeVerifDynamicJoin(employeeId);

                // Assert
                if (result is OkObjectResult okResult)
                {
                    Console.WriteLine("✓ GetDynamicJoin avec employeeId fonctionne");
                    return true;
                }
                else if (result is StatusCodeResult statusResult)
                {
                    Console.WriteLine($"✓ GetDynamicJoin avec employeeId retourne le statut: {statusResult.StatusCode}");
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ GetDynamicJoin avec employeeId a échoué");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur dans TestGetDynamicJoin_WithEmployeeId: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test pour vérifier que GetDynamicTableColumns fonctionne pour la table Employee
        /// </summary>
        public async Task<bool> TestGetDynamicColumns_Employee_ShouldReturnTableStructure()
        {
            try
            {
                // Act
                var result = await _controller.GetDynamicTableColumns("employee");

                // Assert
                if (result is OkObjectResult okResult)
                {
                    Console.WriteLine("✓ GetDynamicColumns pour 'employee' fonctionne");
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ GetDynamicColumns pour 'employee' a échoué");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur dans TestGetDynamicColumns_Employee: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test pour vérifier que GetDynamicTableColumns fonctionne pour la table Verif
        /// </summary>
        public async Task<bool> TestGetDynamicColumns_Verif_ShouldReturnTableStructure()
        {
            try
            {
                // Act
                var result = await _controller.GetDynamicTableColumns("verif");

                // Assert
                if (result is OkObjectResult okResult)
                {
                    Console.WriteLine("✓ GetDynamicColumns pour 'verif' fonctionne");
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ GetDynamicColumns pour 'verif' a échoué");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur dans TestGetDynamicColumns_Verif: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test pour vérifier que GetDynamicTableColumns rejette les tables invalides
        /// </summary>
        public async Task<bool> TestGetDynamicColumns_InvalidTable_ShouldReturnBadRequest()
        {
            try
            {
                // Act
                var result = await _controller.GetDynamicTableColumns("invalid_table");

                // Assert
                if (result is BadRequestObjectResult)
                {
                    Console.WriteLine("✓ GetDynamicColumns rejette correctement les tables invalides");
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ GetDynamicColumns n'a pas rejeté la table invalide");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur dans TestGetDynamicColumns_InvalidTable: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test pour vérifier que UpdateEmployeeVerifDynamic fonctionne
        /// </summary>
        public async Task<bool> TestUpdateEmployeeVerifDynamic_ShouldWork()
        {
            try
            {
                // Arrange
                int employeeId = 1; // Test avec ID 1

                var updateData = new ExpandoObject() as IDictionary<string, object>;
                updateData!["Nom"] = "Test Nom Modifié";
                updateData["Email"] = "<EMAIL>";

                // Act
                var result = await _controller.UpdateEmployeeVerifDynamic(employeeId, (ExpandoObject)updateData);

                // Assert
                if (result is OkObjectResult)
                {
                    Console.WriteLine("✓ UpdateEmployeeVerifDynamic fonctionne");
                    return true;
                }
                else if (result is StatusCodeResult statusResult)
                {
                    Console.WriteLine($"✓ UpdateEmployeeVerifDynamic retourne le statut: {statusResult.StatusCode}");
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ UpdateEmployeeVerifDynamic a échoué");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur dans TestUpdateEmployeeVerifDynamic: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test pour vérifier que UpdateEmployeeVerifDynamic rejette les données vides
        /// </summary>
        public async Task<bool> TestUpdateEmployeeVerifDynamic_EmptyData_ShouldReturnBadRequest()
        {
            try
            {
                // Arrange
                int employeeId = 1;
                var updateData = new ExpandoObject();

                // Act
                var result = await _controller.UpdateEmployeeVerifDynamic(employeeId, updateData);

                // Assert
                if (result is BadRequestObjectResult)
                {
                    Console.WriteLine("✓ UpdateEmployeeVerifDynamic rejette correctement les données vides");
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ UpdateEmployeeVerifDynamic n'a pas rejeté les données vides");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur dans TestUpdateEmployeeVerifDynamic_EmptyData: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Méthode principale pour exécuter tous les tests
        /// </summary>
        public async Task<bool> RunAllTests()
        {
            Console.WriteLine("=== EXÉCUTION DES TESTS DYNAMIC-JOIN ===\n");

            var tests = new List<(string Name, Func<Task<bool>> Test)>
            {
                ("Test GetDynamicJoin", TestGetDynamicJoin_ShouldReturnValidResult),
                ("Test GetDynamicJoin avec ID", TestGetDynamicJoin_WithEmployeeId_ShouldWork),
                ("Test GetDynamicColumns Employee", TestGetDynamicColumns_Employee_ShouldReturnTableStructure),
                ("Test GetDynamicColumns Verif", TestGetDynamicColumns_Verif_ShouldReturnTableStructure),
                ("Test GetDynamicColumns Table Invalide", TestGetDynamicColumns_InvalidTable_ShouldReturnBadRequest),
                ("Test UpdateEmployeeVerifDynamic", TestUpdateEmployeeVerifDynamic_ShouldWork),
                ("Test UpdateEmployeeVerifDynamic Données Vides", TestUpdateEmployeeVerifDynamic_EmptyData_ShouldReturnBadRequest)
            };

            int passed = 0;
            int total = tests.Count;

            foreach (var (name, test) in tests)
            {
                Console.WriteLine($"Exécution: {name}");
                try
                {
                    bool result = await test();
                    if (result) passed++;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ Exception dans {name}: {ex.Message}");
                }
                Console.WriteLine();
            }

            Console.WriteLine($"=== RÉSULTATS ===");
            Console.WriteLine($"Tests réussis: {passed}/{total}");
            Console.WriteLine($"Pourcentage de réussite: {(passed * 100.0 / total):F1}%");

            return passed == total;
        }

        /// <summary>
        /// Méthode statique pour exécuter les tests depuis l'extérieur
        /// </summary>
        public static async Task<bool> ExecuteTests()
        {
            var testInstance = new EmployeeVerifDynamicJoinTests();
            return await testInstance.RunAllTests();
        }
    }
}
