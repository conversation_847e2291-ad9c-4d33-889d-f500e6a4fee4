using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;
using System.Dynamic;
using System.Text;
using System.Text.Json;
using Xunit;

namespace Tests
{
    public class EmployeeVerifDynamicJoinTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public EmployeeVerifDynamicJoinTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task GetDynamicJoin_ShouldReturnAllEmployeesWithVerif()
        {
            // Act
            var response = await _client.GetAsync("/api/EmployeeVerif/dynamic-join");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            
            Assert.NotNull(content);
            Assert.Contains("Data", content);
            Assert.Contains("Metadata", content);
            Assert.Contains("Count", content);
            Assert.Contains("Timestamp", content);
        }

        [Fact]
        public async Task GetDynamicJoin_WithEmployeeId_ShouldReturnSpecificEmployee()
        {
            // Arrange
            int employeeId = 1; // Supposons qu'un employé avec ID 1 existe

            // Act
            var response = await _client.GetAsync($"/api/EmployeeVerif/dynamic-join?employeeId={employeeId}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            
            Assert.NotNull(content);
            Assert.Contains("Data", content);
        }

        [Fact]
        public async Task GetDynamicColumns_Employee_ShouldReturnTableStructure()
        {
            // Act
            var response = await _client.GetAsync("/api/EmployeeVerif/dynamic-columns/employee");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            
            Assert.NotNull(content);
            Assert.Contains("TableName", content);
            Assert.Contains("Columns", content);
            Assert.Contains("ColumnCount", content);
            Assert.Contains("employee", content);
        }

        [Fact]
        public async Task GetDynamicColumns_Verif_ShouldReturnTableStructure()
        {
            // Act
            var response = await _client.GetAsync("/api/EmployeeVerif/dynamic-columns/verif");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            
            Assert.NotNull(content);
            Assert.Contains("TableName", content);
            Assert.Contains("Columns", content);
            Assert.Contains("verif", content);
        }

        [Fact]
        public async Task GetDynamicColumns_InvalidTable_ShouldReturnBadRequest()
        {
            // Act
            var response = await _client.GetAsync("/api/EmployeeVerif/dynamic-columns/invalid_table");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
        }

        [Fact]
        public async Task UpdateEmployeeVerifDynamic_ShouldUpdateSuccessfully()
        {
            // Arrange
            int employeeId = 1; // Supposons qu'un employé avec ID 1 existe
            
            var updateData = new ExpandoObject() as IDictionary<string, object>;
            updateData["Nom"] = "Test Nom Modifié";
            updateData["Email"] = "<EMAIL>";
            updateData["VerifCode"] = "TESTCODE123";

            var json = JsonSerializer.Serialize(updateData);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PutAsync($"/api/EmployeeVerif/dynamic-update/{employeeId}", content);

            // Assert
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                Assert.Contains("Mise à jour réussie", responseContent);
                Assert.Contains("employeeId", responseContent);
            }
            else
            {
                // Si l'employé n'existe pas, on s'attend à une erreur 404 ou 500
                Assert.True(response.StatusCode == System.Net.HttpStatusCode.NotFound || 
                           response.StatusCode == System.Net.HttpStatusCode.InternalServerError);
            }
        }

        [Fact]
        public async Task UpdateEmployeeVerifDynamic_EmptyData_ShouldReturnBadRequest()
        {
            // Arrange
            int employeeId = 1;
            var updateData = new ExpandoObject();
            var json = JsonSerializer.Serialize(updateData);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PutAsync($"/api/EmployeeVerif/dynamic-update/{employeeId}", content);

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
        }

        [Theory]
        [InlineData("employee")]
        [InlineData("verif")]
        [InlineData("pointage")]
        public async Task GetDynamicColumns_AllowedTables_ShouldReturnSuccess(string tableName)
        {
            // Act
            var response = await _client.GetAsync($"/api/EmployeeVerif/dynamic-columns/{tableName}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            Assert.Contains(tableName, content);
        }

        [Fact]
        public async Task DynamicJoin_ShouldIncludeCalculatedProperties()
        {
            // Act
            var response = await _client.GetAsync("/api/EmployeeVerif/dynamic-join");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            
            // Vérifier que les propriétés calculées sont présentes dans les métadonnées
            Assert.Contains("NomComplet", content);
            Assert.Contains("HasVerificationData", content);
            Assert.Contains("VerificationStatus", content);
            Assert.Contains("SecurityLevel", content);
            Assert.Contains("AuthenticationMethods", content);
        }

        [Fact]
        public async Task DynamicJoin_ResponseStructure_ShouldBeValid()
        {
            // Act
            var response = await _client.GetAsync("/api/EmployeeVerif/dynamic-join");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            
            using var document = JsonDocument.Parse(content);
            var root = document.RootElement;
            
            // Vérifier la structure de la réponse
            Assert.True(root.TryGetProperty("Data", out _));
            Assert.True(root.TryGetProperty("Metadata", out _));
            Assert.True(root.TryGetProperty("Count", out _));
            Assert.True(root.TryGetProperty("Timestamp", out _));
            
            // Vérifier la structure des métadonnées
            var metadata = root.GetProperty("Metadata");
            Assert.True(metadata.TryGetProperty("EmployeeTable", out _));
            Assert.True(metadata.TryGetProperty("VerifTable", out _));
            Assert.True(metadata.TryGetProperty("CalculatedProperties", out _));
        }

        [Fact]
        public async Task GetDynamicColumns_ShouldReturnDetailedColumnInfo()
        {
            // Act
            var response = await _client.GetAsync("/api/EmployeeVerif/dynamic-columns/employee");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            
            using var document = JsonDocument.Parse(content);
            var root = document.RootElement;
            
            // Vérifier la structure de la réponse
            Assert.True(root.TryGetProperty("TableName", out _));
            Assert.True(root.TryGetProperty("Columns", out _));
            Assert.True(root.TryGetProperty("ColumnCount", out _));
            Assert.True(root.TryGetProperty("Timestamp", out _));
            
            // Vérifier qu'il y a des colonnes
            var columns = root.GetProperty("Columns");
            Assert.True(columns.GetArrayLength() > 0);
            
            // Vérifier la structure d'une colonne
            if (columns.GetArrayLength() > 0)
            {
                var firstColumn = columns[0];
                Assert.True(firstColumn.TryGetProperty("ColumnName", out _));
                Assert.True(firstColumn.TryGetProperty("DataType", out _));
                Assert.True(firstColumn.TryGetProperty("IsNullable", out _));
                Assert.True(firstColumn.TryGetProperty("Position", out _));
            }
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _client?.Dispose();
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
