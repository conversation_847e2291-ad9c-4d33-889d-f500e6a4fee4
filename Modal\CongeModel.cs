using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LearnAPI.Modal
{
    public class CongeModel
    {
        [Key]
        public int Id { get; set; }

        public int UtilisateurId { get; set; }

        public DateTime? DateDebut { get; set; }

        public DateTime? DateFin { get; set; }

        public string? Motif { get; set; }

        public string? Statut { get; set; }

        public int? ValidePar { get; set; }

        public decimal? heures { get; set; }

        public decimal? solde_conge { get; set; }
    }
}
