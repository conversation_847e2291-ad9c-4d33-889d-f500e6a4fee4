﻿using System;
using System.Collections.Generic;

using LearnAPI.Modal;

using LearnAPI.Repos.Models;
using Microsoft.EntityFrameworkCore;

namespace LearnAPI.Repos;

public partial class LearndataContext : DbContext
{
    public LearndataContext()
    {
    }

    public LearndataContext(DbContextOptions<LearndataContext> options)
        : base(options)
    {
    }


    public virtual DbSet<TblMenu> TblMenus { get; set; }

    public virtual DbSet<TblOtpManager> TblOtpManagers { get; set; }

    public virtual DbSet<TblPwdManger> TblPwdMangers { get; set; }

    public virtual DbSet<TblRefreshtoken> TblRefreshtokens { get; set; }

    public virtual DbSet<TblRole> TblRoles { get; set; }

    public virtual DbSet<TblRolepermission> TblRolepermissions { get; set; }

    public virtual DbSet<TblTempuser> TblTempusers { get; set; }

    public virtual DbSet<TblUser> TblUsers { get; set; }

    public virtual DbSet<TblTva> TblTvas { get; set; }

    public virtual DbSet<Tvamodal> tvadetail { get; set; }

    public virtual DbSet<TblFamille> TblFamilles { get; set; }

    public virtual DbSet<Famillemodal> familledetail { get; set; }

    public virtual DbSet<TblClient> TblClients { get; set; }
    public virtual DbSet<Clientmodal> clientdetail { get; set; }


    public virtual DbSet<TblArticle> TblArticles { get; set; }
    public virtual DbSet<Articlemodal> articledetail { get; set; }



    public virtual DbSet<CA_HT_Par_Moismodal> CA_HT_Par_MoisResults { get; set; }
    
    public virtual DbSet<AuthorizedMac> AuthorizedMacs { get; set; }
   // public virtual DbSet<AuthorizedMac> AuthorizedMacs { get; set; }
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {


        modelBuilder.Entity<CA_HT_Par_Moismodal>()
    .HasNoKey();



        modelBuilder.Entity<TblUser>()
.ToTable("tbl_user", t => t.HasTrigger("AfterInsertuser"));


        
        modelBuilder.Entity<TblTempuser>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("tbl_tempuser1");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
