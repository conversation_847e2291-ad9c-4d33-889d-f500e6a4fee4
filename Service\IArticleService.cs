﻿using LearnAPI.Helper;
using LearnAPI.Modal;


namespace LearnAPI.Service
{
    public interface IArticleService
    {
        Task<List<Articlemodal>> Getall();
        Task<Articlemodal> Getbycode(string code);
        Task<Articlemodal> GetById(int idt);
        Task<APIResponse> Remove(string code);
        Task<APIResponse> Create(Articlemodal data);

        Task<APIResponse> Update(Articlemodal data,string code);
    }
}
