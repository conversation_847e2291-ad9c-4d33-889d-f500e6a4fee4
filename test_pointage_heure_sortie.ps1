# Test script for Pointage API with HeureSortie support
$baseUrl = "https://localhost:7095/api"

Write-Host "=== Test Pointage API avec HeureSortie ===" -ForegroundColor Green

# Test 1: GET all pointages
Write-Host "`n1. Test GET /api/Pointage" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/Pointage" -Method GET -SkipCertificateCheck
    Write-Host "✓ GET réussi. Nombre de pointages: $($response.Count)" -ForegroundColor Green
    if ($response.Count -gt 0) {
        Write-Host "Premier pointage (avec HeureSortie):" -ForegroundColor Cyan
        $response[0] | ConvertTo-Json -Depth 2
    }
} catch {
    Write-Host "✗ Erreur GET: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: POST new pointage with HeureSortie
Write-Host "`n2. Test POST /api/Pointage/add avec HeureSortie" -ForegroundColor Yellow
$newPointage = @{
    UtilisateurId = 1
    Horodatage = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss")
    Type = "Entrée"
    Methode = "badge"
    Lieu = "Bureau principal"
    Statut = "normal"
    HeureSortie = (Get-Date).AddHours(8).ToString("yyyy-MM-ddTHH:mm:ss")
} | ConvertTo-Json

Write-Host "Données envoyées:" -ForegroundColor Cyan
$newPointage

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/Pointage/add" -Method POST -Body $newPointage -ContentType "application/json" -SkipCertificateCheck
    Write-Host "✓ POST réussi. Réponse:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 2
    $newId = $response.Id
} catch {
    Write-Host "✗ Erreur POST: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Détails de l'erreur: $responseBody" -ForegroundColor Red
    }
}

# Test 3: GET specific pointage
if ($newId) {
    Write-Host "`n3. Test GET /api/Pointage/$newId" -ForegroundColor Yellow
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/Pointage/$newId" -Method GET -SkipCertificateCheck
        Write-Host "✓ GET spécifique réussi (vérification HeureSortie):" -ForegroundColor Green
        $response | ConvertTo-Json -Depth 2
    } catch {
        Write-Host "✗ Erreur GET spécifique: $($_.Exception.Message)" -ForegroundColor Red
    }

    # Test 4: PUT update pointage
    Write-Host "`n4. Test PUT /api/Pointage/$newId avec mise à jour HeureSortie" -ForegroundColor Yellow
    $updatePointage = @{
        Id = $newId
        UtilisateurId = 1
        Horodatage = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss")
        Type = "Sortie"
        Methode = "biométrie"
        Lieu = "Bureau principal - Sortie"
        Statut = "normal"
        HeureSortie = (Get-Date).AddHours(9).ToString("yyyy-MM-ddTHH:mm:ss")
    } | ConvertTo-Json

    Write-Host "Données de mise à jour:" -ForegroundColor Cyan
    $updatePointage

    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/Pointage/$newId" -Method PUT -Body $updatePointage -ContentType "application/json" -SkipCertificateCheck
        Write-Host "✓ PUT réussi. Réponse:" -ForegroundColor Green
        $response | ConvertTo-Json -Depth 2
    } catch {
        Write-Host "✗ Erreur PUT: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Détails de l'erreur: $responseBody" -ForegroundColor Red
        }
    }

    # Test 5: DELETE pointage
    Write-Host "`n5. Test DELETE /api/Pointage/$newId" -ForegroundColor Yellow
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/Pointage/$newId" -Method DELETE -SkipCertificateCheck
        Write-Host "✓ DELETE réussi. Réponse:" -ForegroundColor Green
        $response | ConvertTo-Json -Depth 2
    } catch {
        Write-Host "✗ Erreur DELETE: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Tests terminés ===" -ForegroundColor Green
Write-Host "Vérifiez que la colonne HeureSortie est bien présente dans toutes les réponses." -ForegroundColor Cyan
