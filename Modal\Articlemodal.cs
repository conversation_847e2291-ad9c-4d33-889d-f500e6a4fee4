﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using LearnAPI.Repos.Models;

namespace LearnAPI.Modal
{
    [Keyless]
    public class Articlemodal
    {
        [Key]
        public int ida { get; set; }

        [StringLength(50)]
        [Unicode(false)]
        public string Code { get; set; } = null!;

      //  [StringLength(200)]
        public string des { get; set; }

        
        [Column(TypeName = "decimal(20, 3)")]
        public decimal paHT { get; set; }
        
        
        [Column(TypeName = "decimal(20, 3)")]
        public decimal paTTC { get; set; }

        
        [Column(TypeName = "decimal(20, 3)")]
        public decimal marge { get; set; }

        
        [Column(TypeName = "decimal(20, 3)")]
        public decimal pvHT { get; set; }
        
        
        [Column(TypeName = "decimal(20, 3)")]
        public decimal pvTTC { get; set; }
        
     //   [Required]
        [Column(TypeName = "decimal(20, 3)")]
        public decimal qte { get; set; }   

        public string type { get; set; }
        public string emplacement { get; set; }
        public string depo { get; set; }

        public bool? fodec { get; set; }
        public decimal mntFodec { get; set; }

        public bool? IsActive { get; set; }

        public string? Statusname { get; set; }

        // Relations avec Fournisseur, Famille, TVA
        //[Required]
        public int idtFournisseur { get; set; } // ID du fournisseur

        //[Required]
       // [StringLength(50)]
        public string codeFournisseur { get; set; } // Code du fournisseur

        //[Required]
        public int idfFamille { get; set; } // ID de la famille

        //[Required]
      //  [StringLength(50)]
        public string codeFamille { get; set; } // Code de la famille

        //[Required]
        public int idtTva { get; set; } // ID de la TVA

        //[Required]
       // [StringLength(50)]
       // public string codeTva { get; set; } // Code de la TVA
        
        //[Required]
        [Column(TypeName = "decimal(3, 0)")]
        [Range(0, 100)]
        public decimal tauxTva { get; set; }   // Taux de TVA en pourcentage, 0, 7, 13,19.


        public decimal cMP { get; set; }
        public decimal dPA { get; set; }
        public decimal margeP { get; set; } // marge en %
        public string baseCalcul { get; set; }
        public string typeArticle { get; set; }
        public string unité { get; set; } // KG , Metre , 

        public string codeAbarre { get; set; }
        public string refFR { get; set; }
        public decimal remisFR { get; set; }

    


    }
}
