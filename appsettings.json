{
  ///old
  /*
  "Kestrel": {
    "Endpoints": {
      // "LocalNetwork": {
      // "Url": "https://************:4500" // Adresse réseau local
      //   },
      "ZeroTier": {
        "Url": "https://***************:4500" // Adresse ZeroTier
      },
      "Localhost": {
        "Url": "https://127.0.0.1:4500" // Adresse localhost
      }
    }
  },
  */
  ////////////
  /*
  "Kestrel": {
    "Endpoints": {
      "Https": {
        // "Url": "https://************:4500",
        // "Url": "https://***************:4500",
        //"Url": "https://***************:4500", // Adresse ZeroTier
  "Url": "https://*:4500"
     //   "Url": "https://127.0.0.1:4500" // Adresse localhost
        // "Url": "https://127.0.0.1:5600",
      }
    }
  },
  */
  ///////////
  /*"Kestrel": {
    "Endpoints": {
      "ZeroTier": {
        "Url": "https://**************:4500"
      },
      "Localhost": {
        "Url": "https://127.0.0.1:4500"
      },
      "LocalNetwork": {
        "Url": "https://************:4500" // Remplacez par l'adresse IP de votre machine sur le réseau local
      }
    }
  },
  */



  // Development ==>

  //YESS

  "Kestrel": {
    "Endpoints": {
      "LocalNetwork": {
        "Url": "https://*:4500"
      }
    }
  },
  

  /*
  "Kestrel": {
    "Endpoints": {
      "LocalNetwork": {
        "Url": "http://127.0.0.1:4500"
      }
    }
  },
  */

  /*
  //Production ==>
  "Kestrel": {
    "Endpoints": {
      "LocalNetwork": {
        "Url": "https://*:4500"
      }
    }
  },
  */
  ////////////
  "JwtSettings": {
    "securitykey": "thisismyapikeythisismyapikeythisismyapikey"
  },
  "Logging": {
    "Logpath": "F:\\LaernCore\\Logs\\Log.txt",
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "apicon": "Server=DESKTOP-JM9HQ35\\MSSQLSERVER01;Database=Pointage_DB;Trusted_Connection=True;TrustServerCertificate=True;"
  },
  "EmailSettings": {
    "Email": "<EMAIL>", // Adresse e-mail
    "Password": "Mcs27060070", // Mot de passe
    "Host": "mail.megachipsolution.com", // Serveur SMTP
    "Port": 587, // Port SMTP (587 pour STARTTLS)
    "EnableSsl": true // Active SSL/TLS
  }
  //"EmailSettings": {
  //  "Email": "<EMAIL>",
  //  "Password": "Mcs27060070",
  //  "Host": "mail.megachipsolution.com",
  //  "DisplayName": "Mega Chip Solution ERP",
  //  "Port": 995,
  //  // "Port": 587,
  //  "EnableSsl": true
  //}
}


//{ /*
//  "Kestrel": {
//    "Endpoints": {
//      "LocalNetwork": {
//        "Url": "http://************:4500" // Adresse réseau local
//      },
//      "ZeroTier": {
//        //"Url": "http://***************:4500" // Adresse ZeroTier
//      },
//      "Localhost": {
//        "Url": "http://127.0.0.1:4500" // Adresse localhost
//      }
//    }
//  },
//  */
//  "Kestrel": {
//    "Endpoints": {
//      "Https": {
//        // "Url": "http://************:4500",
//        // "Url": "http://***************:4500",
//        //"Url": "http://***************:4500", // Adresse ZeroTier
//        "Url": "http://127.0.0.1:4500" // Adresse localhost
//        // "Url": "http://127.0.0.1:5600",
//      }
//    }
//  },
//  "JwtSettings": {
//    "securitykey": "thisismyapikeythisismyapikeythisismyapikey"
//  },
//  "Logging": {
//    "Logpath": "F:\\LaernCore\\Logs\\Log.txt",
//    "LogLevel": {
//      "Default": "Information",
//      "Microsoft.AspNetCore": "Warning"
//    }
//  },
//  "AllowedHosts": "*",
//  "ConnectionStrings": {
//    "apicon": "Server=.;Database=test_db;Trusted_Connection=True;TrustServerCertificate=True;"
//    //"apicon": "Server=FIRAS;Database=test_db;Trusted_Connection=True;TrustServerCertificate=True;"

//    //"apicon": "Server=DESKTOP-B36GRDG;Database=test_db;Trusted_Connection=True;user id=sa;password=*********;TrustServerCertificate=True;"
//  },
//  // "Host": "smtp.gmail.com",

//  "EmailSettings": {
//    "Email": "<EMAIL>",
//    "Password": "Mcs27060070",
//    "Host": "mail.megachipsolution.com",


//    "DisplayName": "Mega Chip Solution ERP",

//    "Port": 465
//  //  "Port": 587
//  }
//}

