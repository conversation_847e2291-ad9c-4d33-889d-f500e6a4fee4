using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Data.SqlClient;
using Modal;

namespace Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TypePeriodeController : ControllerBase
    {
        private readonly string _connectionString = "Server=DESKTOP-JM9HQ35\\MSSQLSERVER01;Database=Pointage_DB;Trusted_Connection=True;";

        [HttpGet]
        public IActionResult GetAll()
        {
            try
            {
                var types = new List<TypePeriodeModel>();
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("SELECT [id], [nom_type] FROM [Pointage_DB].[dbo].[type_periode]", connection);
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            types.Add(new TypePeriodeModel
                            {
                                id = reader.GetInt32(0),
                                nom_type = reader.IsDBNull(1) ? null : reader.GetString(1)
                            });
                        }
                    }
                }
                return Ok(types);
            }
            catch (System.Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }
        
        [HttpGet("{id}")]
        public IActionResult GetById(int id)
        {
            try
            {
                TypePeriodeModel? type = null;
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("SELECT [id], [nom_type] FROM [Pointage_DB].[dbo].[type_periode] WHERE id = @id", connection);
                    command.Parameters.AddWithValue("@id", id);
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            type = new TypePeriodeModel
                            {
                                id = reader.GetInt32(0),
                                nom_type = reader.IsDBNull(1) ? null : reader.GetString(1)
                            };
                        }
                    }
                }
                if (type == null) return NotFound();
                return Ok(type);
            }
            catch (System.Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpPost]
        public IActionResult Create([FromBody] TypePeriodeModel model)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("INSERT INTO [Pointage_DB].[dbo].[type_periode] ([nom_type]) VALUES (@nom_type); SELECT SCOPE_IDENTITY();", connection);
                    command.Parameters.AddWithValue("@nom_type", (object?)model.nom_type ?? DBNull.Value);
                    connection.Open();
                    var newId = Convert.ToInt32(command.ExecuteScalar());
                    model.id = newId;
                }
                return Ok(model);
            }
            catch (System.Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpPut("{id}")]
        public IActionResult Update(int id, [FromBody] TypePeriodeModel model)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("UPDATE [Pointage_DB].[dbo].[type_periode] SET nom_type = @nom_type WHERE id = @id", connection);
                    command.Parameters.AddWithValue("@id", id);
                    command.Parameters.AddWithValue("@nom_type", (object?)model.nom_type ?? DBNull.Value);
                    connection.Open();
                    var rows = command.ExecuteNonQuery();
                    if (rows == 0) return NotFound();
                }
                return Ok(model);
            }
            catch (System.Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }

        [HttpDelete("{id}")]
        public IActionResult Delete(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var command = new SqlCommand("DELETE FROM [Pointage_DB].[dbo].[type_periode] WHERE id = @id", connection);
                    command.Parameters.AddWithValue("@id", id);
                    connection.Open();
                    var rows = command.ExecuteNonQuery();
                    if (rows == 0) return NotFound();
                }
                return Ok();
            }
            catch (System.Exception ex)
            {
                return StatusCode(500, $"Erreur serveur: {ex.Message}");
            }
        }
    }
}
