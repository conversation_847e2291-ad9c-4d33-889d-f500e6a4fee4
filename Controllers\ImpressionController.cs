﻿using FastReport;
using FastReport.Data;
using FastReport.Export.PdfSimple;
using Microsoft.AspNetCore.Mvc;
using System.IO;

using Microsoft.Extensions.Configuration;
using System;

namespace LearnAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ImpressionController : ControllerBase
    {
        private readonly IConfiguration _configuration;

        // Injection de IConfiguration dans le constructeur
        public ImpressionController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        

        [HttpGet("{reportName}/{Id}")]
        public IActionResult ExportToPdf(string reportName, int Id)
        {
            try
            {
                // Enregistrer le type de connexion
                FastReport.Utils.RegisteredObjects.AddConnection(typeof(MsSqlDataConnection));

                // Charger le fichier de rapport
                var report = new Report();
                string reportPath = Path.Combine("Rapport", $"{reportName}.frx");

                if (!System.IO.File.Exists(reportPath))
                {
                    return NotFound(new { message = "Le fichier de modèle de rapport est introuvable." });
                }

                report.Load(reportPath);

                // Vérifiez si le rapport contient un paramètre nommé "id"
                if (report.Parameters.FindByName("id") == null)
                   // if (!report.Parameters.Contains("id"))
                {
                    return BadRequest(new { message = "Le rapport ne contient pas de paramètre nommé 'id'." });
                }

                //////////////////////////
                // Obtenir la chaîne de connexion depuis appsettings.json
                
                string connectionString = _configuration.GetConnectionString("apicon");

                // Connexion à la base de données
                var connection = new MsSqlDataConnection
                {
                    ConnectionString = connectionString
                };
               


                //////////////
                // Connexion à la base de données
                /*
                var connection = new MsSqlDataConnection
                {
                    ConnectionString = "Server=.;Database=test_db;Trusted_Connection=True;TrustServerCertificate=True;"//"Server=FIRAS;Database=test_db;Trusted_Connection=True;TrustServerCertificate=True;"
                };
                */
                report.Dictionary.Connections.Add(connection);

                // Passer le paramètre "id" au rapport
                report.SetParameterValue("id", Id);

                // Vérification des paramètres
                Console.WriteLine("Paramètre id : " + report.GetParameterValue("id"));

                // Préparer le rapport
                report.Prepare();

                // Exporter le rapport en PDF
                var export = new PDFSimpleExport();
                string outputPath = Path.Combine("pdf", $"{reportName}_{Id}.pdf");
                Directory.CreateDirectory("pdf"); // Créer le dossier si nécessaire
                export.Export(report, outputPath);

                // Nettoyer les ressources
                report.Dispose();

                // Retourner le fichier PDF généré
                var fileBytes = System.IO.File.ReadAllBytes(outputPath);
                return File(fileBytes, "application/pdf", $"{reportName}_{Id}.pdf");
            }
            catch (Exception ex)
            {
                // Gestion des erreurs
                return StatusCode(500, new { message = "Erreur lors de la génération du rapport.", details = ex.Message });
            }
        }
    }
}

/*
using FastReport;
using FastReport.Data;
using FastReport.Export.PdfSimple;
using Microsoft.AspNetCore.Mvc;
using System.IO;

namespace LearnAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ImpressionController : ControllerBase
    {
        [HttpGet("{reportName}/{id}")]
        public IActionResult ExportToPdf(string reportName, int id)
        {
            // Enregistrer le type de connexion (au cas où ce n'est pas déjà fait)
            FastReport.Utils.RegisteredObjects.AddConnection(typeof(MsSqlDataConnection));

            // Charger le fichier de rapport
            var report = new Report();
            string reportPath = Path.Combine("Rapport", $"{reportName}.frx");

            if (!System.IO.File.Exists(reportPath))
            {
                return NotFound(new { message = "Le fichier de modèle de rapport est introuvable." });
            }

            report.Load(reportPath);

            // Connexion à la base de données
            var connection = new MsSqlDataConnection
            {
                ConnectionString = "Server=FIRAS;Database=test_db;Trusted_Connection=True;TrustServerCertificate=True;"
            };
            report.Dictionary.Connections.Add(connection);

            // Passer le paramètre "id" au rapport
            report.SetParameterValue("id", id);

            // Préparer le rapport
            report.Prepare();

            // Exporter le rapport en PDF
            var export = new PDFSimpleExport();
            string outputPath = Path.Combine("pdf", $"{reportName}_{id}.pdf");
            Directory.CreateDirectory("pdf"); // Créer le dossier si nécessaire
            export.Export(report, outputPath);

            // Nettoyer les ressources
            report.Dispose();

            // Retourner le fichier PDF généré
            var fileBytes = System.IO.File.ReadAllBytes(outputPath);
            return File(fileBytes, "application/pdf", $"{reportName}_{id}.pdf");
        }
    }
}
*/