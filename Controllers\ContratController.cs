using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace VotreNamespace.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ContratController : ControllerBase
    {
        private readonly string _connectionString;

        public ContratController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("apicon") 
                ?? throw new ArgumentNullException("La chaîne de connexion est manquante");
        }

        [HttpGet("getAllContrats")]
        public async Task<IActionResult> GetAllContrats()
        {
            try
            {
                var contrats = new List<ContratModel>();
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    var cmd = new SqlCommand("SELECT id, libelle FROM [Pointage_DB].[dbo].[tbl_contrat]", connection);
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            contrats.Add(new ContratModel
                            {
                                Id = reader.GetInt32(0),
                                Libelle = reader.GetString(1)
                            });
                        }
                    }
                }
                return Ok(contrats);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, $"Erreur de connexion à la base de données : {ex.Message}");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur serveur : {ex.Message}");
            }
        }
    }

    public class ContratModel
    {
        public int Id { get; set; }
        public string Libelle { get; set; } = string.Empty;
    }
}
