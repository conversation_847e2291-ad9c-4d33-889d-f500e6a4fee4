﻿using AutoMapper;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.Vml;
using DocumentFormat.OpenXml.Wordprocessing;
using LearnAPI.Container;
using LearnAPI.Helper;
using LearnAPI.Modal;

using LearnAPI.Repos;
using LearnAPI.Repos.Models;
using LearnAPI.Service;
using LearnAPI.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using System.Net.Sockets;
using System.Text;
using System.Net;
using DocumentFormat.OpenXml.InkML;
using System;

using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.FileProviders;


var builder = WebApplication.CreateBuilder(args);
// Configuration dynamique avec rechargement



//YES
builder.WebHost.ConfigureKestrel(serverOptions =>
{
    // La configuration se fait automatiquement via appsettings.json
    // Pas besoin de code supplémentaire
});





/*
//////// Charger les paramètres de Kestrel depuis appsettings.json /ajouté aprés var builder = WebApplication.CreateBuilder(args);
builder.WebHost.ConfigureKestrel((context, options) =>
{

    // Lire l'URL de la configuration (appsettings.json ou appsettings.Development.json)
    var kestrelSection = context.Configuration.GetSection("Kestrel:Endpoints:Http");
    var url = kestrelSection.GetValue<string>("Url");

    if (!string.IsNullOrEmpty(url))
    {
        // Créer un objet URI pour analyser l'adresse et le port
        var uri = new Uri(url);
        options.Listen(System.Net.IPAddress.Parse(uri.Host), uri.Port); // Utilisation dynamique
    }
    else
    {
        // Par défaut, écoute sur http://127.0.0.1:4800 si aucune URL n'est spécifiée
        options.Listen(System.Net.IPAddress.Loopback, 4800);
    }
});
//OK
*/
///
/// 

/*
// Charger les paramètres de Kestrel depuis appsettings.json
builder.WebHost.ConfigureKestrel((context, options) =>
{
    var kestrelSection = context.Configuration.GetSection("Kestrel:Endpoints");
    bool hasValidEndpoint = false;

    foreach (var endpoint in kestrelSection.GetChildren())
    {
        var url = endpoint.GetValue<string>("Url");

        if (!string.IsNullOrEmpty(url))
        {
            try
            {
                var uri = new Uri(url);
                options.Listen(System.Net.IPAddress.Parse(uri.Host), uri.Port);
                Console.WriteLine($"✅ Serveur démarré sur {url}");
                hasValidEndpoint = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Impossible d'écouter sur {url} : {ex.Message}");
            }
        }
    }

    // Si aucune adresse valide, utiliser une valeur par défaut
    if (!hasValidEndpoint)
    {
        options.Listen(System.Net.IPAddress.Loopback, 4800);
        Console.WriteLine($"ℹ️ Aucun endpoint valide, démarrage sur http://127.0.0.1:4800 par défaut");
    }
});
*/
/*
//production
builder.WebHost.ConfigureKestrel((context, options) =>
{
    var kestrelSection = context.Configuration.GetSection("Kestrel:Endpoints");
    bool hasValidEndpoint = false;

    foreach (var endpoint in kestrelSection.GetChildren())
    {
        var url = endpoint.GetValue<string>("Url");

        if (!string.IsNullOrEmpty(url))
        {
            try
            {
                var uri = new Uri(url);
                var ip = uri.Host == "0.0.0.0" ? System.Net.IPAddress.Any : System.Net.IPAddress.Parse(uri.Host);
                //options.Listen(ip, uri.Port, listenOptions =>
                //{
                    
                //   // listenOptions.UseHttps(); // Utilisation de HTTPS
                //});
               options.Listen(ip, uri.Port);

                Console.WriteLine($"✅ Serveur démarré sur {url}");
                hasValidEndpoint = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Impossible d'écouter sur {url} : {ex.Message}");
            }
        }
    }


    // Par défaut, écouter sur localhost:4800
    if (!hasValidEndpoint)
    {
        //options.Listen(System.Net.IPAddress.Loopback, 4800, listenOptions =>
        //{

        //   // listenOptions.UseHttps(); // HTTPS par défaut
        //});

     options.Listen(System.Net.IPAddress.Loopback, 4800);
        Console.WriteLine("ℹ️ Aucun endpoint valide, démarrage sur http://127.0.0.1:4800 par défaut");
    }
});
*/


/*

builder.WebHost.ConfigureKestrel((context, options) =>
{
    var kestrelSection = context.Configuration.GetSection("Kestrel:Endpoints");

    foreach (var endpoint in kestrelSection.GetChildren())
    {
        var url = endpoint["Url"];
        if (!string.IsNullOrEmpty(url))
        {
            var uri = new Uri(url);
            options.Listen(IPAddress.Parse(uri.Host), uri.Port);
        }
    }
});
*/
/////////
//var builder = WebApplication.CreateBuilder(args);

//builder.WebHost.ConfigureKestrel((context, options) =>
//{
//    var endpoints = context.Configuration.GetSection("Kestrel:Endpoints").GetChildren();

//    foreach (var endpoint in endpoints)
//    {
//        var url = endpoint.GetValue<string>("Url");
//        if (!string.IsNullOrEmpty(url))
//        {
//            var uri = new Uri(url);

//            if (uri.Scheme.Equals("https", StringComparison.OrdinalIgnoreCase))
//            {
//                options.Listen(IPAddress.Parse(uri.Host), uri.Port, listenOptions =>
//                {
//                    listenOptions.UseHttps();
//                });
//            }
//            else
//            {
//                options.Listen(IPAddress.Parse(uri.Host), uri.Port);
//            }
//        }
//    }
//});

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
//builder.Services.AddSingleton<IEncryptionService, EncryptionService>();

builder.Services.AddSingleton<IEncryptionService>(new EncryptionService("Mcs27060070@Firass&Ayoub"));
//////
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));
builder.Services.AddTransient<IRefreshHandler, RefreshHandler>();
builder.Services.AddTransient<IUserService, UserService>();
builder.Services.AddTransient<IUserRoleServicecs, UserRoleService>();
builder.Services.AddTransient<IEmailService, EmailService>();

builder.Services.AddTransient<ITvaService, TvaService>();
builder.Services.AddTransient<IFamilleService, FamilleService>();

builder.Services.AddTransient<IClientService, ClientService>();
builder.Services.AddTransient<IArticleService, ArticleService>();



builder.Services.AddDbContext<LearndataContext>(o =>
o.UseSqlServer(builder.Configuration.GetConnectionString("apicon")));

//builder.Services.AddAuthentication("BasicAuthentication").AddScheme<AuthenticationSchemeOptions, BasicAuthenticationHandler>("BasicAuthentication", null);

var _authkey = builder.Configuration.GetValue<string>("JwtSettings:securitykey");
builder.Services.AddAuthentication(item =>
{
    item.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    item.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
}).AddJwtBearer(item =>
{
    item.RequireHttpsMetadata = true;
    item.SaveToken = true;
    item.TokenValidationParameters = new TokenValidationParameters()
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_authkey)),
        ValidateIssuer = false,
        ValidateAudience = false,
        ClockSkew = TimeSpan.Zero
    };

});

var automapper = new MapperConfiguration(item => item.AddProfile(new AutoMapperHandler()));
IMapper mapper = automapper.CreateMapper();
builder.Services.AddSingleton(mapper);
builder.Services.AddCors(p => p.AddPolicy("corspolicy", build =>
{
    build.WithOrigins("*").AllowAnyMethod().AllowAnyHeader();
}));

builder.Services.AddCors(p => p.AddPolicy("corspolicy1", build =>
{
    build.WithOrigins("https://localhost:7249").AllowAnyMethod().AllowAnyHeader();
}));

builder.Services.AddCors(p => p.AddDefaultPolicy(build =>
{
    build.WithOrigins("*").AllowAnyMethod().AllowAnyHeader();
}));

builder.Services.AddRateLimiter(_ => _.AddFixedWindowLimiter(policyName: "fixedwindow", options =>
{
    options.Window = TimeSpan.FromSeconds(10);
    options.PermitLimit = 1;
    options.QueueLimit = 0;
    options.QueueProcessingOrder = System.Threading.RateLimiting.QueueProcessingOrder.OldestFirst;
}).RejectionStatusCode = 401);

string logpath = builder.Configuration.GetSection("Logging:Logpath").Value;
var _logger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .MinimumLevel.Override("microsoft", Serilog.Events.LogEventLevel.Warning)
    .Enrich.FromLogContext()
    .WriteTo.File(logpath)
    .CreateLogger();
builder.Logging.AddSerilog(_logger);

var _jwtsetting = builder.Configuration.GetSection("JwtSettings");
builder.Services.Configure<JwtSettings>(_jwtsetting);


var app = builder.Build();

app.MapGet("/minimalapi", () => "Nihira Techiees");
// Customer Operations

// Customer CRUD Operations

app.MapGet("/getchannel", (string channelname) => "Welcome to " + channelname).WithOpenApi(opt =>
{
    var parameter = opt.Parameters[0];
    parameter.Description = "Enter Channel Name";
    return opt;
});

// TVA Operations

app.MapGet("/gettva", async (LearndataContext db) => await db.TblTvas.ToListAsync());

app.MapGet("/gettvabycode/{code}", async (LearndataContext db, string code) => await db.TblTvas.FindAsync(code));

app.MapPost("/createtva", async (LearndataContext db, TblTva tva) =>
{
    await db.TblTvas.AddAsync(tva);
    await db.SaveChangesAsync();
});

app.MapPut("/updatetva/{code}", async (LearndataContext db, TblTva tva, string code) =>
{
    var existdata = await db.TblTvas.FindAsync(code);
    if (existdata != null)
    {
        existdata.nomtva = tva.nomtva;
        existdata.tauxtva = tva.tauxtva;
        existdata.Name = tva.Name; // Mettez à jour toutes les propriétés nécessaires ici
        await db.SaveChangesAsync();
    }
});

app.MapDelete("/removetva/{code}", async (LearndataContext db, string code) =>
{
    var existdata = await db.TblTvas.FindAsync(code);
    if (existdata != null)
    {
        db.TblTvas.Remove(existdata);
        await db.SaveChangesAsync();
    }
});

// Famille Operations

app.MapGet("/getfamille", async (LearndataContext db) => await db.TblFamilles.ToListAsync());

app.MapGet("/getfamillebycode/{code}", async (LearndataContext db, string code) => await db.TblFamilles.FindAsync(code));

app.MapPost("/createfamille", async (LearndataContext db, TblFamille famille) =>
{
    await db.TblFamilles.AddAsync(famille);
    await db.SaveChangesAsync();
});

app.MapPut("/updatefamille/{code}", async (LearndataContext db, TblFamille famille, string code) =>
{
    var existdata = await db.TblFamilles.FindAsync(code);
    if (existdata != null)
    {
        existdata.nomf = famille.nomf;

        await db.SaveChangesAsync();
    }
});

app.MapDelete("/removefamille/{code}", async (LearndataContext db, string code) =>
{
    var existdata = await db.TblFamilles.FindAsync(code);
    if (existdata != null)
    {
        db.TblFamilles.Remove(existdata);
        await db.SaveChangesAsync();
    }
});


// Client Operations

app.MapGet("/getclient", async (LearndataContext db) => await db.TblClients.ToListAsync());

app.MapGet("/getclientbycode/{code}", async (LearndataContext db, string code) => await db.TblClients.FindAsync(code));

app.MapPost("/createclient", async (LearndataContext db, TblClient client) =>
{
    await db.TblClients.AddAsync(client);
    await db.SaveChangesAsync();
});

app.MapPut("/updateclient/{code}", async (LearndataContext db, TblClient client, string code) =>
{
    var existdata = await db.TblClients.FindAsync(code);
    if (existdata != null)
    {


        existdata.rs = client.rs;
        existdata.adr1 = client.adr1;
        existdata.adr2 = client.adr2;
        existdata.tel1 = client.tel1;
        existdata.tel2 = client.tel2;
        existdata.Email = client.Email;
        existdata.mf = client.mf;
        existdata.type = client.type;

        existdata.exo = client.exo;
        existdata.fodec = client.fodec;

        await db.SaveChangesAsync();
    }
});

app.MapDelete("/removeclient/{code}", async (LearndataContext db, string code) =>
{
    var existdata = await db.TblClients.FindAsync(code);
    if (existdata != null)
    {
        db.TblClients.Remove(existdata);
        await db.SaveChangesAsync();
    }
});

// Article Operations



app.MapGet("/getarticle", async (LearndataContext db) => await db.TblArticles.ToListAsync());

app.MapGet("/getarticlebycode/{code}", async (LearndataContext db, string code) => await db.TblArticles.FindAsync(code));

app.MapPost("/createarticle", async (LearndataContext db, TblArticle article) =>
{
    await db.TblArticles.AddAsync(article);
    await db.SaveChangesAsync();
});

app.MapPut("/updatearticle/{code}", async (LearndataContext db, TblArticle article, string code) =>
{
    var existdata = await db.TblArticles.FindAsync(code);
    if (existdata != null)
    {
        existdata.des = article.des;
        existdata.paHT = article.paHT;
        existdata.paTTC = article.paTTC;
        existdata.marge = article.marge;
        existdata.pvHT = article.pvHT;
        existdata.pvTTC = article.pvTTC;
        existdata.qte = article.qte;

        existdata.idtFournisseur = article.idtFournisseur;
        existdata.codeFournisseur = article.codeFournisseur;
        existdata.idfFamille = article.idfFamille;
        existdata.codeFamille = article.codeFamille;
        existdata.idtTva = article.idtTva;
        existdata.tauxTva = article.tauxTva;
        existdata.type = article.type;
        existdata.emplacement = article.emplacement;
        existdata.depo = article.depo;
        existdata.fodec = article.fodec;
        existdata.mntFodec = article.mntFodec;
        await db.SaveChangesAsync();


    }
});

app.MapDelete("/removearticle/{code}", async (LearndataContext db, string code) =>
{
    var existdata = await db.TblArticles.FindAsync(code);
    if (existdata != null)
    {
        db.TblArticles.Remove(existdata);
        await db.SaveChangesAsync();
    }
});


////////////////////////////
app.UseRateLimiter();
// Configure the HTTP request pipeline.
//if (app.Environment.IsDevelopment())
//{
app.UseSwagger();
app.UseSwaggerUI();
//}
//Servir les fichiers statiques (Angular) /ajouté aprés app.UseSwaggerUI();
//app.UseStaticFiles();
app.UseStaticFiles();

app.UseCors();

app.UseHttpsRedirection();

app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();
// Ajout du fallback pour Angular  /ajouté aprés app.MapControllers();
app.MapFallbackToFile("index.html");

//app.Run();


try
{
    app.Run();
}
catch (Exception ex)
{
    Console.WriteLine($"🔥 Erreur critique au démarrage : {ex.Message}");
}
