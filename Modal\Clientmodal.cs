﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace LearnAPI.Modal
{
    [Keyless]
    public class Clientmodal
    {
        [Key]
        public int idt { get; set; }

       // [StringLength(50)]
        [Unicode(false)]
        public string Code { get; set; } = null!;

     //   [StringLength(50)]
        
        public string? rs { get; set; }

  //      [StringLength(100)]
        
        public string? adr1 { get; set; }

  //      [StringLength(100)]
        
        public string? adr2 { get; set; }

  //      [StringLength(50)]
        
        public string? tel1 { get; set; }

 //       [StringLength(50)]
        
        public string? tel2 { get; set; }

  //      [StringLength(100)]
       
        public string? Email { get; set; }

  //      [StringLength(50)]
        
        public string? mf { get; set; }

 //       [StringLength(50)]
        
        public string? type { get; set; }

        public bool? exo { get; set; }
        public bool? fodec { get; set; }

        public bool? IsActive { get; set; }

        public string? Statusname { get; set; }
    }
}
