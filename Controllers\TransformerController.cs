﻿using FastReport.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;

using Microsoft.Extensions.Configuration;
using System;

// Classe pour représenter la requête reçue par l'API
public class TransformRequest
{
    public int Id { get; set; }
}

[Route("api/[controller]")]
[ApiController]
public class TransformerController : ControllerBase
{
    
    private readonly IConfiguration _configuration;
    private readonly string _connectionString;

    // Injection de IConfiguration pour récupérer la chaîne de connexion
    public TransformerController(IConfiguration configuration)
    {
        _configuration = configuration;
        _connectionString = _configuration.GetConnectionString("apicon");
    }

    //    private readonly string _connectionString = "Server=.;Database=test_db;Trusted_Connection=True;TrustServerCertificate=True;";
    [HttpPost("storedProcedure")]
    public async Task<IActionResult> CallStoredProcedure([FromBody] TransformRequest request)
    {
        try
        {
            await ExecuteStoredProcedureAsync(request.Id);
            return Ok(new { success = true, message = "Transformation réussie" });
        }
        catch (SqlException sqlEx)
        {
            // Extraire le message spécifique de SQL Server
            return BadRequest(new { success = false, message = sqlEx.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = ex.Message });
        }
    }

    private async Task ExecuteStoredProcedureAsync(int id)
    {
        using (var connection = new SqlConnection(_connectionString))
        {
            using (var command = new SqlCommand("Proc_Transform", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@Id", id);

                await connection.OpenAsync();
                await command.ExecuteNonQueryAsync();
            }
        }
    }

}


/*
[HttpPost("storedProcedure")]
public async Task<IActionResult> CallStoredProcedure([FromBody] TransformRequest request)
{
    if (request == null || request.Id <= 0)
    {
        return BadRequest("L'ID est requis et doit être un entier positif.");
    }

    try
    {
        await ExecuteStoredProcedureAsync(request.Id);
        return Ok("Procédure exécutée avec succès.");
    }
    catch (SqlException sqlEx)
    {
        return StatusCode(500, $"Erreur SQL : {sqlEx.Message}");
    }
    catch (Exception ex)
    {
        return StatusCode(500, $"Erreur interne : {ex.Message}");
    }
}

private async Task ExecuteStoredProcedureAsync(int id)
{
    using (var connection = new SqlConnection(_connectionString))
    {
        using (var command = new SqlCommand("Proc_Transform", connection))
        {
            command.CommandType = CommandType.StoredProcedure;
            command.Parameters.AddWithValue("@Id", id);

            await connection.OpenAsync();
            await command.ExecuteNonQueryAsync();
        }
    }
}

}



/*
* 

private readonly IConfiguration _configuration;

// Injection de IConfiguration dans le constructeur
public TransformerController(IConfiguration configuration)
{
    _configuration = configuration;
}
//////////////////////////
// Obtenir la chaîne de connexion depuis appsettings.json

  string connectionString = _configuration.GetConnectionString("apicon");

  // Connexion à la base de données
  var connection = new MsSqlDataConnection
  {
      ConnectionString = connectionString
  };



//////////////


private readonly string _connectionString = _configuration.GetConnectionString("apicon");
// private readonly string _connectionString = "Server=.;Database=test_db;Trusted_Connection=True;TrustServerCertificate=True;";//"Server=FIRAS;Database=test_db;Trusted_Connection=True;TrustServerCertificate=True;";

*
using Microsoft.AspNetCore.Mvc;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;


public class TransformRequest
{
public int Id { get; set; }
}

[Route("api/[controller]")]
[ApiController]
public class TransformerController : ControllerBase
{
private readonly string _connectionString = "Server=FIRAS;Database=test_db;Trusted_Connection=True;TrustServerCertificate=True;";

[HttpPost("stored-procedure")]
public async Task<IActionResult> CallStoredProcedure([FromBody] TransformRequest request)
{
    if (request == null || request.Id <= 0)
    {
        return BadRequest("L'ID est requis et doit être un entier positif.");
    }

    await ExecuteStoredProcedureAsync(request.Id);
    return Ok("Procédure exécutée avec succès.");
}

private async Task ExecuteStoredProcedureAsync(int id)
{
    using (var connection = new SqlConnection(_connectionString))
    {
        using (var command = new SqlCommand("Proc_Transform", connection))
        {
            command.CommandType = CommandType.StoredProcedure;
            command.Parameters.AddWithValue("@Id", id);
            await connection.OpenAsync();
            await command.ExecuteNonQueryAsync();
        }
    }
}
}
*/
/*
[Route("api/[controller]")]
[ApiController]
public class TransformerController : ControllerBase
{
    private readonly string _connectionString = "Server=FIRAS;Database=test_db;Trusted_Connection=True;TrustServerCertificate=True;";

    [HttpPost("stored-procedure")]
    public async Task<IActionResult> CallStoredProcedure([FromBody] dynamic data)
    {
        int id = data.id;
        await ExecuteStoredProcedureAsync(id);
        return Ok("Procédure exécutée avec succès.");
    }

    private async Task ExecuteStoredProcedureAsync(int id)
    {
        using (var connection = new SqlConnection(_connectionString))
        {
            using (var command = new SqlCommand("Proc_Transform", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@Id", id);
                await connection.OpenAsync();
                await command.ExecuteNonQueryAsync();
            }
        }
    }
}
*/